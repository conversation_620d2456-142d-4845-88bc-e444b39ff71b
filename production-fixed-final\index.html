<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carnet de Vaccination Électronique</title>
    <meta name="description" content="Consultation du carnet de vaccination électronique - Vérifiez vos vaccinations en ligne">
    <link rel="icon" type="image/svg+xml" href="./check/vite.svg">
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.15);
            border: 1px solid #e3f2fd;
            text-align: center;
        }
        .icon { font-size: 48px; margin-bottom: 20px; color: #ff9800; }
        h1 { color: #1b5e20; margin-bottom: 10px; font-size: 24px; font-weight: 600; }
        p { color: #e65100; margin-bottom: 30px; font-size: 16px; }
        .form { display: flex; flex-direction: column; gap: 20px; }
        .input-group { text-align: left; }
        label { display: block; margin-bottom: 8px; font-weight: 600; color: #1b5e20; }
        input {
            width: 100%; padding: 12px 16px; border: 2px solid #b0bec5;
            border-radius: 8px; font-size: 16px; background-color: #fafafa;
            transition: border-color 0.3s;
        }
        input:focus { outline: none; border-color: #ff9800; background-color: white; }
        .btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; border: none; padding: 14px 28px; border-radius: 8px;
            font-size: 16px; font-weight: 600; cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; background: #90a4ae; }
        .error { color: #d32f2f; margin-top: 10px; font-weight: 500; }
        .loading { color: #ff9800; margin-top: 10px; }

        /* Styles pour les animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Media queries pour responsive - GARDE LA DISPOSITION HORIZONTALE */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                padding: 15px;
                margin: 5px;
            }

            /* En-tête responsive - GARDE flex horizontal */
            .header-responsive {
                padding: 10px 8px !important;
            }

            .header-responsive .logo-sante {
                width: 35px !important;
                height: 35px !important;
            }

            .header-responsive .logo-inhp {
                width: 60px !important;
                height: 60px !important;
            }

            .header-responsive h2 {
                font-size: 14px !important;
            }

            .header-responsive p {
                font-size: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                margin: 5px;
            }

            /* En-tête mobile - GARDE la disposition horizontale */
            .header-responsive {
                padding: 8px 5px !important;
            }

            .header-responsive .logo-sante {
                width: 28px !important;
                height: 28px !important;
            }

            .header-responsive .logo-inhp {
                width: 45px !important;
                height: 45px !important;
            }

            .header-responsive h2 {
                font-size: 12px !important;
            }

            .header-responsive p {
                font-size: 8px !important;
            }
        }

        /* Styles responsive pour la carte de vaccination - GARDE LA DISPOSITION HORIZONTALE */
        @media (max-width: 768px) {
            .card-header-responsive {
                padding: 6px 8px !important;
            }

            .card-header-responsive .logo-sante-card {
                width: 32px !important;
                height: 32px !important;
            }

            .card-header-responsive .logo-inhp-card {
                width: 65px !important;
                height: 65px !important;
            }

            .card-header-responsive h2 {
                font-size: 15px !important;
            }

            .card-header-responsive p {
                font-size: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .card-header-responsive {
                padding: 5px 4px !important;
            }

            .card-header-responsive .logo-sante-card {
                width: 28px !important;
                height: 28px !important;
            }

            .card-header-responsive .logo-inhp-card {
                width: 50px !important;
                height: 50px !important;
            }

            .card-header-responsive h2 {
                font-size: 13px !important;
            }

            .card-header-responsive p {
                font-size: 9px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- En-tête avec les deux logos -->
        <div class="header-responsive" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); border-radius: 10px; color: white;">
            <!-- Logo Santé à gauche -->
            <div style="flex: 0 0 60px;">
                <img src="./assets/logo_sante.jpg" alt="Ministère de la Santé" class="logo-sante" style="width: 40px; height: 40px; border-radius: 5px; object-fit: contain;">
            </div>

            <!-- Texte au centre -->
            <div class="text-container" style="flex: 1; text-align: center;">
                <h2 style="margin: 0; font-size: 16px; font-weight: 700;">CARNET DE VACCINATION</h2>
                <p style="margin: 0; font-size: 11px; color: #1b5e20; font-weight: 600;">République de Côte d'Ivoire</p>
            </div>

            <!-- Logo INHP à droite -->
            <div style="flex: 0 0 100px; text-align: right;">
                <img src="./assets/logo_inhp.jpg" alt="INHP" class="logo-inhp" style="width: 100px; height: 100px; border-radius: 12px; object-fit: contain;">
            </div>
        </div>

        <h1>Vérification de la vaccination contre la fièvre jaune</h1>
        <p>Veuillez saisir votre numéro précedé de l'indicatif et cliquez sur le bouton "Vérifiez"</p>
        
        <form class="form" id="vaccinForm">
            <div class="input-group">
                <label for="phone">Numéro de téléphone</label>
                <input type="tel" id="phone" placeholder="Ex: 2250769989178" required>
            </div>
            <button type="submit" class="btn" id="submitBtn">Vérifiez</button>
        </form>
        
        <div id="message"></div>
    </div>

    <!-- Résultats en dehors du container pour être visibles -->
    <div id="results"></div>

    <script>
        // Variables globales
        let vaccinationData = [];
        let lastSearchedNumber = '';

        // Attendre que le DOM soit chargé
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            // Vérifier s'il faut restaurer les résultats
            const shouldShowResults = localStorage.getItem('showResultsOnLoad');
            if (shouldShowResults === 'true') {
                const savedData = localStorage.getItem('vaccinationData');
                const savedNumber = localStorage.getItem('lastSearchedNumber');

                if (savedData) {
                    vaccinationData = JSON.parse(savedData);
                    lastSearchedNumber = savedNumber;

                    // Nettoyer le localStorage
                    localStorage.removeItem('showResultsOnLoad');
                    localStorage.removeItem('vaccinationData');
                    localStorage.removeItem('lastSearchedNumber');

                    // Afficher les résultats
                    showResults(vaccinationData);
                    return;
                }
            }

            const form = document.getElementById('vaccinForm');
            const phoneInput = document.getElementById('phone');
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');

            if (!form || !phoneInput || !submitBtn || !messageDiv || !resultsDiv) {
                return;
            }

            // Ajouter l'événement au formulaire
            form.addEventListener('submit', handleFormSubmit);
        }

        async function handleFormSubmit(e) {
            e.preventDefault();

            const phoneInput = document.getElementById('phone');
            const phoneNumber = phoneInput.value.trim();

            if (!phoneNumber) {
                showMessage('Veuillez saisir un numéro de téléphone', 'error');
                return;
            }

            if (!/^[1-9]\d{9,14}$/.test(phoneNumber)) {
                showMessage('Format invalide. Utilisez le format: 2250769989178 (sans le +)', 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tel: phoneNumber, d: 'PROD' })
                });

                const data = await response.json();

                if (data.code === 0 && data.data) {
                    // Sauvegarder le numéro recherché
                    lastSearchedNumber = phoneNumber;

                    if (data.data.length === 1) {
                        // Si un seul résultat, afficher directement les détails
                        vaccinationData = data.data;
                        showDetails(0, true);
                    } else {
                        // Si plusieurs résultats, afficher le tableau
                        showResults(data.data);
                    }
                } else {
                    showMessage(data.msg || 'Carnet non trouvé', 'error');
                }
            } catch (error) {
                showMessage('Erreur de connexion au serveur. Veuillez réessayer.', 'error');
            } finally {
                setLoading(false);
            }
        }



        function setLoading(loading) {
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.disabled = loading;
                submitBtn.textContent = loading ? 'Vérification...' : 'Vérifiez';
            }
            if (loading) {
                showMessage('Recherche en cours...', 'loading');
            }
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            if (messageDiv) {
                messageDiv.innerHTML = `<div class="${type}">${text}</div>`;
            }
        }

        function showResults(records) {
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');
            const container = document.querySelector('.container');

            if (messageDiv) messageDiv.innerHTML = '';
            if (container) {
                container.style.display = 'none';
            }

            const html = `
                <div style="max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; padding: 30px; box-shadow: 0 10px 30px rgba(27, 94, 32, 0.15); border: 1px solid #c8e6c9;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; flex-wrap: wrap; gap: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <span style="font-size: 32px; color: #ff9800;">📋💉</span>
                            <div>
                                <h2 style="color: #1b5e20; margin: 0; font-weight: 600;">Carnet de Vaccination Électronique</h2>
                                <p style="color: #e65100; margin: 0; font-weight: 500;">${records.length} vaccination(s) trouvée(s)</p>
                            </div>
                        </div>
                        <button onclick="location.reload()" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);">
                            Nouvelle recherche
                        </button>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%);">
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Patient</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Type de Vaccin</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Date de Vaccination</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Centre</th>
                                    <th style="padding: 15px; text-align: center; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${records.map((record, index) => `
                                    <tr style="transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='white'">
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            <div>
                                                <div style="font-weight: 600; color: #1b5e20;">${record.NOMPAT} ${record.PRENOMPAT}</div>
                                                <div style="font-size: 14px; color: #e65100;">+${record.NUMEROPAT}</div>
                                            </div>
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            <span style="background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; box-shadow: 0 2px 4px rgba(27, 94, 32, 0.3);">
                                                ${record.NOMVAC}
                                            </span>
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            ${new Date(record.PRESENCE).toLocaleDateString('fr-FR')}
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            ${record.NOMCENTR}
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle; text-align: center;">
                                            <button onclick="showDetails(${index})" style="
                                                background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
                                                color: white; border: none; padding: 10px 16px; border-radius: 8px;
                                                cursor: pointer; font-size: 14px; font-weight: 600;
                                                transition: all 0.3s ease; box-shadow: 0 3px 6px rgba(255, 152, 0, 0.3);
                                                display: flex; align-items: center; gap: 6px; min-width: 100px;
                                                justify-content: center; margin: 0 auto;
                                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 12px rgba(255, 152, 0, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 3px 6px rgba(255, 152, 0, 0.3)'">
                                                <span style="font-size: 16px;">👁️</span>
                                                <span style="font-size: 13px; letter-spacing: 0.5px;">Détails</span>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            if (resultsDiv) {
                resultsDiv.innerHTML = html;
            }
            vaccinationData = records; // Stocker les données pour la vue détaillée
        }

        function showDetails(index, fromDirectCall = false) {
            const record = vaccinationData[index];

            if (!record) {
                return;
            }

            // Cacher le formulaire et les résultats
            const container = document.querySelector('.container');
            const resultsDiv = document.getElementById('results');

            if (container) container.style.display = 'none';
            if (resultsDiv) resultsDiv.style.display = 'none';

            const formatDate = (dateString) => {
                const date = new Date(dateString);
                return date.toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric'
                });
            };

            const calculateAge = (birthDate) => {
                const birth = new Date(birthDate);
                const today = new Date();
                let age = today.getFullYear() - birth.getFullYear();
                const monthDiff = today.getMonth() - birth.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--;
                }

                return age;
            };

            const detailsHtml = `
                <div style="min-height: 100vh; background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%); padding: 20px; display: flex; align-items: center; justify-content: center;">
                    <div class="fade-in" style="width: 100%; max-width: 450px;">
                        <!-- Boutons de navigation flottants -->
                        <div style="text-align: center; margin-bottom: 20px;">
                            ${vaccinationData.length > 1 ? `
                                <button onclick="goBackToResults()" style="background: rgba(27, 94, 32, 0.9); color: white; border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 600; margin-right: 10px; box-shadow: 0 4px 12px rgba(27, 94, 32, 0.3); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    ← Retour aux résultats
                                </button>
                            ` : ''}
                            <button onclick="location.reload()" style="background: rgba(255, 152, 0, 0.9); color: white; border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 600; box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                🔍 Nouvelle recherche
                            </button>
                        </div>

                        <!-- Carte d'identité vaccination -->
                        <div style="background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 25px rgba(27, 94, 32, 0.15); border: 1px solid #c8e6c9; position: relative;">
                            <!-- En-tête de la carte -->
                            <div class="card-header-responsive" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 8px 15px; text-align: center; position: relative;">
                                <div style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                    <!-- Logo Santé à gauche -->
                                    <div style="flex: 0 0 80px;">
                                        <img src="./assets/logo_sante.jpg" alt="Ministère de la Santé" class="logo-sante-card" style="width: 40px; height: 40px; border-radius: 5px; object-fit: contain;">
                                    </div>

                                    <!-- Texte au centre, aligné avec les logos -->
                                    <div style="flex: 1; transform: translateY(8px);">
                                        <h2 style="margin: 0; font-size: 18px; font-weight: 700;">CARNET DE VACCINATION</h2>
                                        <p style="margin: 0; font-size: 12px; color: #1b5e20; font-weight: 600;">République de Côte d'Ivoire</p>
                                    </div>

                                    <!-- Logo INHP à droite -->
                                    <div style="flex: 0 0 90px;">
                                        <img src="./assets/logo_inhp.jpg" alt="INHP" class="logo-inhp-card" style="width: 90px; height: 90px; border-radius: 12px; object-fit: contain;">
                                    </div>
                                </div>
                            </div>

                            <!-- Corps de la carte -->
                            <div style="padding: 20px;">
                                <!-- Section patient -->
                                <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                                    <!-- Photo -->
                                    <div style="flex-shrink: 0;">
                                        ${record.PHOTOPAT ? `
                                            <img
                                                src="https://opisms.net/ecarnet/upload/photo/${record.PHOTOPAT}"
                                                alt="Photo du patient"
                                                style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; border: 2px solid #1b5e20; box-shadow: 0 3px 8px rgba(27, 94, 32, 0.2);"
                                                onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\"color: #546e7a; padding: 25px 15px; border: 2px dashed #b0bec5; border-radius: 8px; background: #f9f9f9; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 24px;\\">
                                                    
                                        ` : `
                                            <div style="color: #1b5e20; padding: 25px 15px; border: 2px dashed #4caf50; border-radius: 8px; background: #f1f8e9; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 24px;">
                                                📷
                                            </div>
                                        `}
                                    </div>

                                    <!-- Informations patient -->
                                    <div style="flex: 1; min-width: 0;">
                                        <h3 style="margin: 0 0 8px 0; color: #1b5e20; font-size: 18px; font-weight: 700; text-transform: uppercase; letter-spacing: 0.5px;">
                                            ${record.NOMPAT} ${record.PRENOMPAT}
                                        </h3>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Sexe:</span><br>
                                                <span style="color: #37474f;">${record.SEXEPAT === 'M' ? '👨 Masculin' : '👩 Féminin'}</span>
                                            </div>
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Né(e) le:</span><br>
                                                <span style="color: #37474f;">${formatDate(record.DATEPAT)}</span>
                                            </div>
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Téléphone:</span><br>
                                                <span style="color: #37474f;">📱 +${record.NUMEROPAT}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>




                                <!-- Section vaccination -->
                                <div style="border-top: 2px solid #e3f2fd; padding-top: 15px;">
                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <div style="display: inline-flex; align-items: center; gap: 8px; background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 700; box-shadow: 0 3px 8px rgba(27, 94, 32, 0.3);">
                                            <span style="font-size: 16px;">🛡️</span>
                                            Vaccin ${record.NOMVAC.toLowerCase()} effectué
                                        </div>
                                    </div>

                                    <div style="background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%); border-radius: 10px; padding: 15px; border: 1px solid #4caf50;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 13px;">
                                            <div>
                                                <span style="color: #e65100; font-weight: 600;">📅 Date:</span><br>
                                                <span style="color: #1b5e20; font-weight: 600;">${formatDate(record.PRESENCE)}</span>
                                            </div>
                                            <div>
                                                <span style="color: #e65100; font-weight: 600;">🏷️ Lot N°:</span><br>
                                                <span style="color: #1b5e20; font-weight: 600;">${record.LOVAC}</span>
                                            </div>
                                        </div>
                                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #4caf50;">
                                            <span style="color: #e65100; font-weight: 600; font-size: 12px;">🏥 Centre de vaccination:</span><br>
                                            <span style="color: #1b5e20; font-weight: 600; font-size: 13px;">${record.NOMCENTR}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pied de carte -->
                                <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #e0e0e0; text-align: center;">
                                    <div style="font-size: 11px; color: #999; line-height: 1.3;">
                                        <strong>Le vaccin de fièvre jaune effectué</strong><br>
                                        Carnet de vaccination électronique certifié
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remplacer le contenu de la page
            document.body.innerHTML = detailsHtml;
        }

        function goBackToResults() {
            // Sauvegarder les données dans le localStorage
            localStorage.setItem('vaccinationData', JSON.stringify(vaccinationData));
            localStorage.setItem('lastSearchedNumber', lastSearchedNumber);
            localStorage.setItem('showResultsOnLoad', 'true');

            // Recharger la page
            location.reload();
        }




    </script>
</body>
</html>