<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="page.title">Navigation - Carnet de Vaccination</title>
    
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }
        
        /* Sélecteur de langue */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .language-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .language-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            padding: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        
        .language-button:hover {
            background: #4caf50;
            color: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .language-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            margin-top: 8px;
        }
        
        .language-dropdown.open .language-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .language-option:last-child {
            border-bottom: none;
        }
        
        .language-option:hover {
            background: #f1f8e9;
            color: #2e7d32;
        }
        
        .language-option.active {
            background: #4caf50;
            color: white;
        }
        
        .header {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .content {
            padding: 30px;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .nav-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #4caf50;
        }
        
        .nav-card .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .nav-card h3 {
            color: #1b5e20;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .nav-card p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .nav-card .btn {
            background: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .nav-card .btn:hover {
            background: #45a049;
        }
        
        .nav-card.primary {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
        }
        
        .nav-card.primary h3 {
            color: white;
        }
        
        .nav-card.primary p {
            color: rgba(255,255,255,0.9);
        }
        
        .nav-card.primary .btn {
            background: white;
            color: #2e7d32;
        }
        
        .nav-card.primary .btn:hover {
            background: #f5f5f5;
        }
        
        .info-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 12px;
            margin: 30px 0;
            text-align: center;
        }
        
        .info-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .info-section p {
            color: #666;
            line-height: 1.6;
        }
        
        /* Support RTL pour l'arabe */
        .rtl {
            direction: rtl;
            text-align: right;
        }
        
        .rtl .language-selector {
            left: 20px;
            right: auto;
        }
        
        .rtl .language-menu {
            left: 0;
            right: auto;
        }
        
        .rtl .nav-card {
            text-align: right;
        }
        
        .rtl .info-section {
            text-align: right;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .language-selector {
                top: 15px;
                right: 15px;
            }
            
            .language-button {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            
            .header {
                padding: 20px;
            }
            
            .content {
                padding: 20px;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sélecteur de langue -->
        <div class="language-selector">
            <div class="language-dropdown" id="languageDropdown">
                <button class="language-button" onclick="toggleLanguageMenu()" title="Changer de langue / Change language / تغيير اللغة">
                    <span class="language-icon">🌐</span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <div class="language-option active" onclick="changeLanguage('fr')">
                        <span class="flag">🇫🇷</span>
                        <span>Français</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('en')">
                        <span class="flag">🇬🇧</span>
                        <span>English</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('ar')">
                        <span class="flag">🇸🇦</span>
                        <span>العربية</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="header">
            <h1 data-translate="nav.title">🧭 Navigation - Carnet de Vaccination</h1>
        </div>
        
        <div class="content">
            <div class="info-section">
                <h3 data-translate="nav.info.title">🌐 Application Multilingue</h3>
                <p data-translate="nav.info.desc">Toutes les pages principales supportent maintenant 3 langues : Français, English et العربية avec support RTL complet.</p>
            </div>
            
            <div class="nav-grid">
                <div class="nav-card primary">
                    <div class="icon">🏠</div>
                    <h3 data-translate="nav.main.title">Application Principale</h3>
                    <p data-translate="nav.main.desc">Vérifiez votre carnet de vaccination avec le système de double recherche (téléphone ou identité).</p>
                    <a href="index.html" class="btn" data-translate="nav.main.btn">Accéder</a>
                </div>
                
                <div class="nav-card">
                    <div class="icon">👋</div>
                    <h3 data-translate="nav.welcome.title">Page d'Accueil</h3>
                    <p data-translate="nav.welcome.desc">Page de bienvenue avec présentation des fonctionnalités et design moderne.</p>
                    <a href="welcome.html" class="btn" data-translate="nav.welcome.btn">Visiter</a>
                </div>
                
                <div class="nav-card">
                    <div class="icon">🆘</div>
                    <h3 data-translate="nav.help.title">Centre d'Aide</h3>
                    <p data-translate="nav.help.desc">Guide complet d'utilisation avec instructions détaillées et résolution de problèmes.</p>
                    <a href="help.html" class="btn" data-translate="nav.help.btn">Consulter</a>
                </div>
                
                <div class="nav-card">
                    <div class="icon">🔄</div>
                    <h3 data-translate="nav.backup.title">Version de Secours</h3>
                    <p data-translate="nav.backup.desc">Version simplifiée de l'application avec fonctionnalités essentielles.</p>
                    <a href="index-backup.html" class="btn" data-translate="nav.backup.btn">Utiliser</a>
                </div>
                
                <div class="nav-card">
                    <div class="icon">🧪</div>
                    <h3 data-translate="nav.test.title">Test des Langues</h3>
                    <p data-translate="nav.test.desc">Page de test pour vérifier le fonctionnement du système multilingue.</p>
                    <a href="test-languages.html" class="btn" data-translate="nav.test.btn">Tester</a>
                </div>
                
                <div class="nav-card">
                    <div class="icon">🔍</div>
                    <h3 data-translate="nav.verification.title">Vérification Build</h3>
                    <p data-translate="nav.verification.desc">Outils de vérification et diagnostic pour le déploiement en production.</p>
                    <a href="verification-finale.html" class="btn" data-translate="nav.verification.btn">Vérifier</a>
                </div>
            </div>
            
            <div class="info-section">
                <h3 data-translate="nav.features.title">✨ Fonctionnalités Multilingues</h3>
                <p data-translate="nav.features.desc">
                    • Traduction complète de l'interface utilisateur<br>
                    • Support RTL pour l'arabe<br>
                    • Sauvegarde automatique des préférences<br>
                    • Sélecteur responsive avec icône 🌐<br>
                    • Messages d'erreur traduits dynamiquement
                </p>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'fr';

        // Traductions
        const translations = {
            fr: {
                'page.title': 'Navigation - Carnet de Vaccination',
                'nav.title': '🧭 Navigation - Carnet de Vaccination',
                'nav.info.title': '🌐 Application Multilingue',
                'nav.info.desc': 'Toutes les pages principales supportent maintenant 3 langues : Français, English et العربية avec support RTL complet.',
                'nav.main.title': 'Application Principale',
                'nav.main.desc': 'Vérifiez votre carnet de vaccination avec le système de double recherche (téléphone ou identité).',
                'nav.main.btn': 'Accéder',
                'nav.welcome.title': 'Page d\'Accueil',
                'nav.welcome.desc': 'Page de bienvenue avec présentation des fonctionnalités et design moderne.',
                'nav.welcome.btn': 'Visiter',
                'nav.help.title': 'Centre d\'Aide',
                'nav.help.desc': 'Guide complet d\'utilisation avec instructions détaillées et résolution de problèmes.',
                'nav.help.btn': 'Consulter',
                'nav.backup.title': 'Version de Secours',
                'nav.backup.desc': 'Version simplifiée de l\'application avec fonctionnalités essentielles.',
                'nav.backup.btn': 'Utiliser',
                'nav.test.title': 'Test des Langues',
                'nav.test.desc': 'Page de test pour vérifier le fonctionnement du système multilingue.',
                'nav.test.btn': 'Tester',
                'nav.verification.title': 'Vérification Build',
                'nav.verification.desc': 'Outils de vérification et diagnostic pour le déploiement en production.',
                'nav.verification.btn': 'Vérifier',
                'nav.features.title': '✨ Fonctionnalités Multilingues',
                'nav.features.desc': '• Traduction complète de l\'interface utilisateur<br>• Support RTL pour l\'arabe<br>• Sauvegarde automatique des préférences<br>• Sélecteur responsive avec icône 🌐<br>• Messages d\'erreur traduits dynamiquement'
            },
            en: {
                'page.title': 'Navigation - Vaccination Card',
                'nav.title': '🧭 Navigation - Vaccination Card',
                'nav.info.title': '🌐 Multilingual Application',
                'nav.info.desc': 'All main pages now support 3 languages: Français, English and العربية with complete RTL support.',
                'nav.main.title': 'Main Application',
                'nav.main.desc': 'Verify your vaccination card with the dual search system (phone or identity).',
                'nav.main.btn': 'Access',
                'nav.welcome.title': 'Welcome Page',
                'nav.welcome.desc': 'Welcome page with feature presentation and modern design.',
                'nav.welcome.btn': 'Visit',
                'nav.help.title': 'Help Center',
                'nav.help.desc': 'Complete usage guide with detailed instructions and troubleshooting.',
                'nav.help.btn': 'Consult',
                'nav.backup.title': 'Backup Version',
                'nav.backup.desc': 'Simplified version of the application with essential features.',
                'nav.backup.btn': 'Use',
                'nav.test.title': 'Language Test',
                'nav.test.desc': 'Test page to verify the multilingual system functionality.',
                'nav.test.btn': 'Test',
                'nav.verification.title': 'Build Verification',
                'nav.verification.desc': 'Verification and diagnostic tools for production deployment.',
                'nav.verification.btn': 'Verify',
                'nav.features.title': '✨ Multilingual Features',
                'nav.features.desc': '• Complete user interface translation<br>• RTL support for Arabic<br>• Automatic preference saving<br>• Responsive selector with 🌐 icon<br>• Dynamically translated error messages'
            },
            ar: {
                'page.title': 'التنقل - بطاقة التطعيم',
                'nav.title': '🧭 التنقل - بطاقة التطعيم',
                'nav.info.title': '🌐 تطبيق متعدد اللغات',
                'nav.info.desc': 'جميع الصفحات الرئيسية تدعم الآن 3 لغات: Français و English و العربية مع دعم RTL كامل.',
                'nav.main.title': 'التطبيق الرئيسي',
                'nav.main.desc': 'تحقق من بطاقة التطعيم الخاصة بك مع نظام البحث المزدوج (الهاتف أو الهوية).',
                'nav.main.btn': 'الوصول',
                'nav.welcome.title': 'صفحة الترحيب',
                'nav.welcome.desc': 'صفحة ترحيب مع عرض الميزات والتصميم الحديث.',
                'nav.welcome.btn': 'زيارة',
                'nav.help.title': 'مركز المساعدة',
                'nav.help.desc': 'دليل استخدام شامل مع تعليمات مفصلة وحل المشاكل.',
                'nav.help.btn': 'استشارة',
                'nav.backup.title': 'نسخة احتياطية',
                'nav.backup.desc': 'نسخة مبسطة من التطبيق مع الميزات الأساسية.',
                'nav.backup.btn': 'استخدام',
                'nav.test.title': 'اختبار اللغات',
                'nav.test.desc': 'صفحة اختبار للتحقق من وظائف النظام متعدد اللغات.',
                'nav.test.btn': 'اختبار',
                'nav.verification.title': 'التحقق من البناء',
                'nav.verification.desc': 'أدوات التحقق والتشخيص لنشر الإنتاج.',
                'nav.verification.btn': 'تحقق',
                'nav.features.title': '✨ ميزات متعددة اللغات',
                'nav.features.desc': '• ترجمة كاملة لواجهة المستخدم<br>• دعم RTL للعربية<br>• حفظ تلقائي للتفضيلات<br>• محدد متجاوب مع أيقونة 🌐<br>• رسائل خطأ مترجمة ديناميكياً'
            }
        };

        // Fonctions de gestion des langues (identiques aux autres pages)
        function toggleLanguageMenu() {
            const dropdown = document.getElementById('languageDropdown');
            dropdown.classList.toggle('open');
        }

        function changeLanguage(lang) {
            currentLanguage = lang;
            
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeLanguage('${lang}')"]`).classList.add('active');
            
            applyTranslations(lang);
            
            if (lang === 'ar') {
                document.body.classList.add('rtl');
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.body.classList.remove('rtl');
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', lang);
            }
            
            document.getElementById('languageDropdown').classList.remove('open');
            localStorage.setItem('selectedLanguage', lang);
        }

        function applyTranslations(lang) {
            const texts = translations[lang];
            
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (texts[key]) {
                    if (element.tagName === 'TITLE') {
                        element.textContent = texts[key];
                    } else {
                        element.innerHTML = texts[key];
                    }
                }
            });
        }

        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageDropdown');
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'fr';
            if (savedLanguage !== 'fr') {
                changeLanguage(savedLanguage);
            }
        });
    </script>
</body>
</html>
