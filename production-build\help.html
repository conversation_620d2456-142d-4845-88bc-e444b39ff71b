<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="page.title">Aide - Carnet de Vaccination</title>
    
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }
        
        /* Sélecteur de langue */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .language-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .language-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            padding: 8px;
            cursor: pointer;
            font-size: 18px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        
        .language-button:hover {
            background: #4caf50;
            color: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .language-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 150px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            margin-top: 5px;
        }
        
        .language-dropdown.open .language-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
            font-weight: 500;
        }
        
        .language-option:last-child {
            border-bottom: none;
        }
        
        .language-option:hover {
            background: #f1f8e9;
            color: #2e7d32;
        }
        
        .language-option.active {
            background: #4caf50;
            color: white;
        }
        
        .header {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        
        .content {
            padding: 30px;
        }
        
        .help-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #4caf50;
        }
        
        .help-section h3 {
            color: #1b5e20;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .help-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .help-section ol, .help-section ul {
            color: #666;
            line-height: 1.6;
        }
        
        .help-section li {
            margin-bottom: 8px;
        }
        
        .back-button {
            background: #4caf50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .back-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        /* Support RTL pour l'arabe */
        .rtl {
            direction: rtl;
            text-align: right;
        }
        
        .rtl .language-selector {
            left: 20px;
            right: auto;
        }
        
        .rtl .language-menu {
            left: 0;
            right: auto;
        }
        
        .rtl .help-section {
            border-left: none;
            border-right: 4px solid #4caf50;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .language-selector {
                top: 15px;
                right: 15px;
            }
            
            .language-button {
                width: 36px;
                height: 36px;
                font-size: 16px;
            }
            
            .header {
                padding: 20px;
            }
            
            .content {
                padding: 20px;
            }
            
            .help-section {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sélecteur de langue -->
        <div class="language-selector">
            <div class="language-dropdown" id="languageDropdown">
                <button class="language-button" onclick="toggleLanguageMenu()" title="Changer de langue / Change language / تغيير اللغة">
                    <span class="language-icon">🌐</span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <div class="language-option active" onclick="changeLanguage('fr')">
                        <span class="flag">🇫🇷</span>
                        <span>Français</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('en')">
                        <span class="flag">🇬🇧</span>
                        <span>English</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('ar')">
                        <span class="flag">🇸🇦</span>
                        <span>العربية</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="header">
            <h1 data-translate="help.title">🆘 Centre d'Aide</h1>
        </div>
        
        <div class="content">
            <div class="help-section">
                <h3 data-translate="help.how.title">📱 Comment utiliser l'application ?</h3>
                <ol>
                    <li data-translate="help.how.step1">Choisissez votre méthode de recherche (téléphone ou identité)</li>
                    <li data-translate="help.how.step2">Saisissez vos informations dans les champs requis</li>
                    <li data-translate="help.how.step3">Cliquez sur "Vérifiez" pour consulter votre carnet</li>
                    <li data-translate="help.how.step4">Consultez les résultats affichés</li>
                </ol>
            </div>
            
            <div class="help-section">
                <h3 data-translate="help.phone.title">📞 Recherche par téléphone</h3>
                <p data-translate="help.phone.desc">Saisissez votre numéro de téléphone avec l'indicatif du pays, sans le signe +.</p>
                <p data-translate="help.phone.example"><strong>Exemple :</strong> 2250769989178 (pour un numéro ivoirien)</p>
            </div>
            
            <div class="help-section">
                <h3 data-translate="help.identity.title">👤 Recherche par identité</h3>
                <p data-translate="help.identity.desc">Remplissez tous les champs obligatoires :</p>
                <ul>
                    <li data-translate="help.identity.lastname">Nom de famille (en majuscules)</li>
                    <li data-translate="help.identity.firstname">Prénom</li>
                    <li data-translate="help.identity.birthdate">Date de naissance (format : jj/mm/aaaa)</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h3 data-translate="help.language.title">🌐 Changer de langue</h3>
                <p data-translate="help.language.desc">Cliquez sur l'icône 🌐 en haut à droite pour choisir votre langue :</p>
                <ul>
                    <li data-translate="help.language.fr">🇫🇷 Français</li>
                    <li data-translate="help.language.en">🇬🇧 English</li>
                    <li data-translate="help.language.ar">🇸🇦 العربية (avec support RTL)</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h3 data-translate="help.problems.title">⚠️ Problèmes courants</h3>
                <ul>
                    <li data-translate="help.problems.notfound"><strong>Carnet non trouvé :</strong> Vérifiez vos informations et réessayez</li>
                    <li data-translate="help.problems.format"><strong>Format invalide :</strong> Respectez le format demandé pour chaque champ</li>
                    <li data-translate="help.problems.connection"><strong>Erreur de connexion :</strong> Vérifiez votre connexion internet</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h3 data-translate="help.security.title">🛡️ Sécurité et confidentialité</h3>
                <p data-translate="help.security.desc">Vos données personnelles sont protégées et ne sont utilisées que pour la vérification de votre statut vaccinal. Aucune information n'est stockée sur nos serveurs.</p>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" class="back-button" data-translate="help.back">
                    🏠 Retour à l'application
                </a>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'fr';

        // Traductions
        const translations = {
            fr: {
                'page.title': 'Aide - Carnet de Vaccination',
                'help.title': '🆘 Centre d\'Aide',
                'help.how.title': '📱 Comment utiliser l\'application ?',
                'help.how.step1': 'Choisissez votre méthode de recherche (téléphone ou identité)',
                'help.how.step2': 'Saisissez vos informations dans les champs requis',
                'help.how.step3': 'Cliquez sur "Vérifiez" pour consulter votre carnet',
                'help.how.step4': 'Consultez les résultats affichés',
                'help.phone.title': '📞 Recherche par téléphone',
                'help.phone.desc': 'Saisissez votre numéro de téléphone avec l\'indicatif du pays, sans le signe +.',
                'help.phone.example': 'Exemple : 2250769989178 (pour un numéro ivoirien)',
                'help.identity.title': '👤 Recherche par identité',
                'help.identity.desc': 'Remplissez tous les champs obligatoires :',
                'help.identity.lastname': 'Nom de famille (en majuscules)',
                'help.identity.firstname': 'Prénom',
                'help.identity.birthdate': 'Date de naissance (format : jj/mm/aaaa)',
                'help.language.title': '🌐 Changer de langue',
                'help.language.desc': 'Cliquez sur l\'icône 🌐 en haut à droite pour choisir votre langue :',
                'help.language.fr': '🇫🇷 Français',
                'help.language.en': '🇬🇧 English',
                'help.language.ar': '🇸🇦 العربية (avec support RTL)',
                'help.problems.title': '⚠️ Problèmes courants',
                'help.problems.notfound': 'Carnet non trouvé : Vérifiez vos informations et réessayez',
                'help.problems.format': 'Format invalide : Respectez le format demandé pour chaque champ',
                'help.problems.connection': 'Erreur de connexion : Vérifiez votre connexion internet',
                'help.security.title': '🛡️ Sécurité et confidentialité',
                'help.security.desc': 'Vos données personnelles sont protégées et ne sont utilisées que pour la vérification de votre statut vaccinal. Aucune information n\'est stockée sur nos serveurs.',
                'help.back': '🏠 Retour à l\'application'
            },
            en: {
                'page.title': 'Help - Vaccination Card',
                'help.title': '🆘 Help Center',
                'help.how.title': '📱 How to use the application?',
                'help.how.step1': 'Choose your search method (phone or identity)',
                'help.how.step2': 'Enter your information in the required fields',
                'help.how.step3': 'Click "Verify" to check your card',
                'help.how.step4': 'Review the displayed results',
                'help.phone.title': '📞 Search by phone',
                'help.phone.desc': 'Enter your phone number with country code, without the + sign.',
                'help.phone.example': 'Example: 2250769989178 (for an Ivorian number)',
                'help.identity.title': '👤 Search by identity',
                'help.identity.desc': 'Fill in all required fields:',
                'help.identity.lastname': 'Last name (in uppercase)',
                'help.identity.firstname': 'First name',
                'help.identity.birthdate': 'Date of birth (format: dd/mm/yyyy)',
                'help.language.title': '🌐 Change language',
                'help.language.desc': 'Click the 🌐 icon in the top right to choose your language:',
                'help.language.fr': '🇫🇷 Français',
                'help.language.en': '🇬🇧 English',
                'help.language.ar': '🇸🇦 العربية (with RTL support)',
                'help.problems.title': '⚠️ Common problems',
                'help.problems.notfound': 'Card not found: Check your information and try again',
                'help.problems.format': 'Invalid format: Follow the required format for each field',
                'help.problems.connection': 'Connection error: Check your internet connection',
                'help.security.title': '🛡️ Security and privacy',
                'help.security.desc': 'Your personal data is protected and used only for verifying your vaccination status. No information is stored on our servers.',
                'help.back': '🏠 Back to application'
            },
            ar: {
                'page.title': 'المساعدة - بطاقة التطعيم',
                'help.title': '🆘 مركز المساعدة',
                'help.how.title': '📱 كيفية استخدام التطبيق؟',
                'help.how.step1': 'اختر طريقة البحث (الهاتف أو الهوية)',
                'help.how.step2': 'أدخل معلوماتك في الحقول المطلوبة',
                'help.how.step3': 'انقر على "تحقق" لفحص بطاقتك',
                'help.how.step4': 'راجع النتائج المعروضة',
                'help.phone.title': '📞 البحث بالهاتف',
                'help.phone.desc': 'أدخل رقم هاتفك مع رمز البلد، بدون علامة +.',
                'help.phone.example': 'مثال: 2250769989178 (لرقم إيفواري)',
                'help.identity.title': '👤 البحث بالهوية',
                'help.identity.desc': 'املأ جميع الحقول المطلوبة:',
                'help.identity.lastname': 'اسم العائلة (بأحرف كبيرة)',
                'help.identity.firstname': 'الاسم الأول',
                'help.identity.birthdate': 'تاريخ الميلاد (التنسيق: يوم/شهر/سنة)',
                'help.language.title': '🌐 تغيير اللغة',
                'help.language.desc': 'انقر على أيقونة 🌐 في الأعلى يميناً لاختيار لغتك:',
                'help.language.fr': '🇫🇷 الفرنسية',
                'help.language.en': '🇬🇧 الإنجليزية',
                'help.language.ar': '🇸🇦 العربية (مع دعم RTL)',
                'help.problems.title': '⚠️ المشاكل الشائعة',
                'help.problems.notfound': 'البطاقة غير موجودة: تحقق من معلوماتك وحاول مرة أخرى',
                'help.problems.format': 'تنسيق غير صحيح: اتبع التنسيق المطلوب لكل حقل',
                'help.problems.connection': 'خطأ في الاتصال: تحقق من اتصالك بالإنترنت',
                'help.security.title': '🛡️ الأمان والخصوصية',
                'help.security.desc': 'بياناتك الشخصية محمية وتُستخدم فقط للتحقق من حالة التطعيم. لا يتم تخزين أي معلومات على خوادمنا.',
                'help.back': '🏠 العودة إلى التطبيق'
            }
        };

        // Fonctions de gestion des langues (identiques aux autres pages)
        function toggleLanguageMenu() {
            const dropdown = document.getElementById('languageDropdown');
            dropdown.classList.toggle('open');
        }

        function changeLanguage(lang) {
            currentLanguage = lang;
            
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeLanguage('${lang}')"]`).classList.add('active');
            
            applyTranslations(lang);
            
            if (lang === 'ar') {
                document.body.classList.add('rtl');
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.body.classList.remove('rtl');
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', lang);
            }
            
            document.getElementById('languageDropdown').classList.remove('open');
            localStorage.setItem('selectedLanguage', lang);
        }

        function applyTranslations(lang) {
            const texts = translations[lang];
            
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (texts[key]) {
                    if (element.tagName === 'TITLE') {
                        element.textContent = texts[key];
                    } else {
                        element.textContent = texts[key];
                    }
                }
            });
        }

        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageDropdown');
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'fr';
            if (savedLanguage !== 'fr') {
                changeLanguage(savedLanguage);
            }
        });
    </script>
</body>
</html>
