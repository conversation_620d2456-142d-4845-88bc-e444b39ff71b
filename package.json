{"name": "qrcode-vaccin-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --outDir production-final", "deploy": "npm run build && powershell -Command \"If (Test-Path .\\mon_dossier_deploiement) { Remove-Item -Recurse -Force .\\mon_dossier_deploiement\\* } Else { New-Item -ItemType Directory -Force -Path .\\mon_dossier_deploiement }; Copy-Item -Recurse -Force .\\production-final\\* .\\mon_dossier_deploiement\\ \\\""}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "dependencies": {"axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}