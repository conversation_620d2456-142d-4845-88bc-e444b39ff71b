<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - Carnet de Vaccination</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test de Diagnostic</h1>
        
        <div class="test-info">
            <h3>✅ Statut du serveur</h3>
            <p>Si vous voyez cette page, le serveur fonctionne correctement.</p>
            <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
        </div>
        
        <div class="test-info">
            <h3>🔍 Tests à effectuer</h3>
            <ol>
                <li>Vérifiez les logs du serveur</li>
                <li>Testez les ressources (images, CSS, JS)</li>
                <li>Vérifiez les permissions des fichiers</li>
                <li>Testez les appels API</li>
            </ol>
        </div>
        
        <div class="test-info">
            <h3>📁 Fichiers requis</h3>
            <ul>
                <li>index.html</li>
                <li>logo_sante.jpg</li>
                <li>logo_inhp.jpg</li>
                <li>vite.svg</li>
            </ul>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('fr-FR');
        
        // Test simple de fonctionnalité JavaScript
        console.log('✅ JavaScript fonctionne correctement');
        
        // Test de fetch (sans appel réel)
        console.log('🔗 Prêt pour les tests d\'API');
    </script>
</body>
</html>
