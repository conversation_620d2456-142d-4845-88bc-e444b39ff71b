// Types pour l'API de vaccination
export interface VaccinationRecord {
  IDCAL: string;
  NOMVAC: string;
  DATERAPEL: string;
  PRESENCE: string;
  LOVAC: string;
  NOMCENTR: string;
  IDPAT: string;
  NOMPAT: string;
  PRENOMPAT: string;
  NUMEROPAT: string;
  NUMEROPAT2: string;
  DATEPAT: string;
  SEXEPAT: string;
}

export interface ApiResponse {
  code: number;
  msg: string;
  data?: VaccinationRecord[];
}

export interface ApiRequest {
  tel: string;
  d: string;
}
