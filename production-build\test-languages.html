<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Langues - Carnet de Vaccination</title>
    
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1b5e20;
            text-align: center;
            margin-bottom: 30px;
        }
        .language-test {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        .language-test h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .flag {
            font-size: 24px;
            margin-right: 15px;
        }
        .language-info {
            flex: 1;
        }
        .language-name {
            font-weight: 600;
            color: #1b5e20;
            font-size: 16px;
        }
        .language-details {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .test-button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .rtl-demo {
            direction: rtl;
            text-align: right;
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .feature-list {
            background: #f1f8e9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .feature-list ul {
            margin-left: 20px;
        }
        .feature-list li {
            margin: 8px 0;
        }
        .demo-text {
            background: white;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .demo-text.arabic {
            direction: rtl;
            text-align: right;
            font-family: 'Arial', sans-serif;
        }
        .demo-text.english {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-text.french {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Test du Système de Langues</h1>
        
        <div class="test-section">
            <h3>📋 Langues Supportées</h3>
            <p>L'application supporte maintenant 3 langues avec changement dynamique :</p>
            
            <div class="test-item">
                <span class="flag">🇫🇷</span>
                <div class="language-info">
                    <div class="language-name">Français</div>
                    <div class="language-details">Langue par défaut - Direction LTR</div>
                </div>
                <button class="test-button" onclick="testLanguage('fr')">Tester</button>
            </div>
            
            <div class="test-item">
                <span class="flag">🇬🇧</span>
                <div class="language-info">
                    <div class="language-name">English</div>
                    <div class="language-details">Langue internationale - Direction LTR</div>
                </div>
                <button class="test-button" onclick="testLanguage('en')">Tester</button>
            </div>
            
            <div class="test-item">
                <span class="flag">🇸🇦</span>
                <div class="language-info">
                    <div class="language-name">العربية</div>
                    <div class="language-details">Support RTL complet - Direction RTL</div>
                </div>
                <button class="test-button" onclick="testLanguage('ar')">Tester</button>
            </div>
        </div>
        
        <div class="language-test">
            <h3>✨ Fonctionnalités Implémentées</h3>
            <div class="feature-list">
                <ul>
                    <li>✅ <strong>Sélecteur icône 🌐</strong> compact et responsive</li>
                    <li>✅ <strong>Design circulaire</strong> optimisé pour mobile</li>
                    <li>✅ <strong>Traduction complète</strong> de tous les textes de l'interface</li>
                    <li>✅ <strong>Support RTL</strong> pour l'arabe (direction droite-gauche)</li>
                    <li>✅ <strong>Sauvegarde de préférence</strong> dans localStorage</li>
                    <li>✅ <strong>Messages d'erreur traduits</strong> dynamiquement</li>
                    <li>✅ <strong>Placeholders traduits</strong> dans les champs de saisie</li>
                    <li>✅ <strong>Animations fluides</strong> avec effet hover</li>
                    <li>✅ <strong>Responsive design</strong> adaptatif selon la taille d'écran</li>
                </ul>
            </div>
        </div>
        
        <div class="language-test">
            <h3>🎯 Exemples de Traductions</h3>
            
            <div class="demo-text french">
                <strong>🇫🇷 Français :</strong><br>
                "Vérification de la vaccination contre la fièvre jaune"<br>
                "Choisissez votre méthode de recherche"
            </div>
            
            <div class="demo-text english">
                <strong>🇬🇧 English :</strong><br>
                "Yellow fever vaccination verification"<br>
                "Choose your search method"
            </div>
            
            <div class="demo-text arabic">
                <strong>🇸🇦 العربية :</strong><br>
                "التحقق من تطعيم الحمى الصفراء"<br>
                "اختر طريقة البحث"
            </div>
        </div>
        
        <div class="language-test">
            <h3>🔧 Support RTL pour l'Arabe</h3>
            <p>Démonstration du support Right-to-Left :</p>
            <div class="rtl-demo">
                <h4>مثال على النص العربي</h4>
                <p>هذا النص يظهر من اليمين إلى اليسار كما هو مطلوب في اللغة العربية. جميع عناصر الواجهة تتكيف تلقائياً مع اتجاه النص.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 Instructions de Test</h3>
            <ol>
                <li><strong>Accédez à l'application principale</strong> : <a href="index.html" target="_blank">index.html</a></li>
                <li><strong>Cliquez sur l'icône 🌐</strong> en haut à droite (design circulaire)</li>
                <li><strong>Testez chaque langue</strong> : Français, English, العربية</li>
                <li><strong>Vérifiez les traductions</strong> de tous les éléments</li>
                <li><strong>Testez le mode RTL</strong> avec l'arabe (icône se déplace à gauche)</li>
                <li><strong>Testez sur mobile</strong> pour voir l'adaptation responsive</li>
                <li><strong>Rechargez la page</strong> pour vérifier la sauvegarde</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>📱 Test Mobile</h3>
            <p>L'icône 🌐 s'adapte parfaitement aux écrans mobiles :</p>
            <ul>
                <li><strong>Design circulaire compact</strong> : 44px → 40px → 36px selon l'écran</li>
                <li><strong>Position optimisée</strong> pour le tactile (coin supérieur)</li>
                <li><strong>Icône universelle</strong> : 🌐 reconnaissable sur tous appareils</li>
                <li><strong>Menu responsive</strong> : s'adapte à la taille d'écran</li>
                <li><strong>Support RTL</strong> : icône se déplace à gauche en arabe</li>
                <li><strong>Tooltip multilingue</strong> : aide contextuelle</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="background: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px;">
                🏠 Tester l'Application
            </a>
            <a href="verification-finale.html" style="background: #2196f3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px;">
                🔍 Vérification Complète
            </a>
        </div>
    </div>
    
    <script>
        function testLanguage(lang) {
            const messages = {
                fr: "✅ Test Français : Toutes les traductions françaises sont actives !",
                en: "✅ English Test: All English translations are active!",
                ar: "✅ اختبار العربية: جميع الترجمات العربية نشطة!"
            };
            
            alert(messages[lang]);
            
            // Ouvrir l'application avec la langue sélectionnée
            localStorage.setItem('selectedLanguage', lang);
            window.open('index.html', '_blank');
        }
        
        // Afficher des informations sur le navigateur
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Test des langues initialisé');
            console.log('📱 User Agent:', navigator.userAgent);
            console.log('🌍 Langue du navigateur:', navigator.language);
        });
    </script>
</body>
</html>
