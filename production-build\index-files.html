<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Index des Fichiers - Build Production</title>
    
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .content {
            padding: 30px;
        }
        .file-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #4caf50;
        }
        .file-section h3 {
            color: #1b5e20;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .file-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }
        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #4caf50;
        }
        .file-name {
            font-weight: 600;
            color: #1b5e20;
            margin-bottom: 5px;
        }
        .file-desc {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
        }
        .file-link {
            background: #4caf50;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            transition: background 0.3s;
        }
        .file-link:hover {
            background: #45a049;
        }
        .stats {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 12px;
            margin: 30px 0;
            text-align: center;
        }
        .stats h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #1976d2;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .primary-actions {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 30px 0;
            text-align: center;
        }
        .primary-actions h3 {
            margin-bottom: 20px;
            color: white;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .action-btn {
            background: white;
            color: #2e7d32;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            background: #f5f5f5;
            transform: translateY(-2px);
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            .header {
                padding: 20px;
            }
            .content {
                padding: 20px;
            }
            .file-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 Index des Fichiers - Build Production</h1>
            <p>Version 2.2.0 - Build Multilingue Complet</p>
        </div>
        
        <div class="content">
            <div class="primary-actions">
                <h3>🚀 Actions Principales</h3>
                <div class="action-buttons">
                    <a href="index.html" class="action-btn">🏠 Application Principale</a>
                    <a href="welcome.html" class="action-btn">👋 Page d'Accueil</a>
                    <a href="navigation.html" class="action-btn">🧭 Navigation</a>
                    <a href="verification-finale.html" class="action-btn">✅ Vérification</a>
                </div>
            </div>
            
            <div class="stats">
                <h3>📊 Statistiques du Build</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">35</div>
                        <div class="stat-label">Fichiers Total</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">Pages Principales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Langues</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">90+</div>
                        <div class="stat-label">Éléments Traduits</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">9</div>
                        <div class="stat-label">Pages de Test</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">10</div>
                        <div class="stat-label">Docs</div>
                    </div>
                </div>
            </div>
            
            <div class="file-section">
                <h3>🏠 Pages Principales Multilingues</h3>
                <div class="file-grid">
                    <div class="file-item">
                        <div class="file-name">index.html</div>
                        <div class="file-desc">Application principale complète (920+ lignes)</div>
                        <a href="index.html" class="file-link">Ouvrir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">welcome.html</div>
                        <div class="file-desc">Page d'accueil moderne avec présentation</div>
                        <a href="welcome.html" class="file-link">Ouvrir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">help.html</div>
                        <div class="file-desc">Centre d'aide complet multilingue</div>
                        <a href="help.html" class="file-link">Ouvrir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">navigation.html</div>
                        <div class="file-desc">Hub de navigation entre toutes les pages</div>
                        <a href="navigation.html" class="file-link">Ouvrir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">index-backup.html</div>
                        <div class="file-desc">Version de secours simplifiée</div>
                        <a href="index-backup.html" class="file-link">Ouvrir</a>
                    </div>
                </div>
            </div>
            
            <div class="file-section">
                <h3>🧪 Pages de Test et Diagnostic</h3>
                <div class="file-grid">
                    <div class="file-item">
                        <div class="file-name">test-languages.html</div>
                        <div class="file-desc">Test complet du système multilingue</div>
                        <a href="test-languages.html" class="file-link">Tester</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">verification-finale.html</div>
                        <div class="file-desc">Vérification complète du build</div>
                        <a href="verification-finale.html" class="file-link">Vérifier</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">test-favicon.html</div>
                        <div class="file-desc">Test des favicons officiels</div>
                        <a href="test-favicon.html" class="file-link">Tester</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">demo-responsive-language.html</div>
                        <div class="file-desc">Démonstration du sélecteur responsive</div>
                        <a href="demo-responsive-language.html" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">diagnostic.html</div>
                        <div class="file-desc">Diagnostic technique complet</div>
                        <a href="diagnostic.html" class="file-link">Diagnostiquer</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">fix-favicon.html</div>
                        <div class="file-desc">Solutions pour problèmes de favicon</div>
                        <a href="fix-favicon.html" class="file-link">Réparer</a>
                    </div>
                </div>
            </div>
            
            <div class="file-section">
                <h3>📚 Documentation Complète</h3>
                <div class="file-grid">
                    <div class="file-item">
                        <div class="file-name">BUILD-COMPLET.md</div>
                        <div class="file-desc">Résumé final complet du build</div>
                        <a href="BUILD-COMPLET.md" class="file-link">Lire</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">CHECKLIST-FINAL.md</div>
                        <div class="file-desc">Checklist de validation finale</div>
                        <a href="CHECKLIST-FINAL.md" class="file-link">Vérifier</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">DEPLOY-NOW.md</div>
                        <div class="file-desc">Instructions de déploiement immédiat</div>
                        <a href="DEPLOY-NOW.md" class="file-link">Déployer</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">LANGUES-DOCUMENTATION.md</div>
                        <div class="file-desc">Documentation du système multilingue</div>
                        <a href="LANGUES-DOCUMENTATION.md" class="file-link">Consulter</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">PAGES-TRADUITES.md</div>
                        <div class="file-desc">Résumé des pages traduites</div>
                        <a href="PAGES-TRADUITES.md" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">README.md</div>
                        <div class="file-desc">Guide principal d'utilisation</div>
                        <a href="README.md" class="file-link">Lire</a>
                    </div>
                </div>
            </div>
            
            <div class="file-section">
                <h3>🖼️ Assets et Configuration</h3>
                <div class="file-grid">
                    <div class="file-item">
                        <div class="file-name">favicon.ico</div>
                        <div class="file-desc">Favicon principal officiel</div>
                        <a href="favicon.ico" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">site.webmanifest</div>
                        <div class="file-desc">Manifest PWA pour installation</div>
                        <a href="site.webmanifest" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">logo_sante.jpg</div>
                        <div class="file-desc">Logo Ministère de la Santé</div>
                        <a href="logo_sante.jpg" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">logo_inhp.jpg</div>
                        <div class="file-desc">Logo INHP</div>
                        <a href="logo_inhp.jpg" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">robots.txt</div>
                        <div class="file-desc">Configuration SEO</div>
                        <a href="robots.txt" class="file-link">Voir</a>
                    </div>
                    <div class="file-item">
                        <div class="file-name">sitemap.xml</div>
                        <div class="file-desc">Plan du site pour SEO</div>
                        <a href="sitemap.xml" class="file-link">Voir</a>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f1f8e9; border-radius: 12px;">
                <h3 style="color: #2e7d32; margin-bottom: 15px;">🎉 Build de Production Complet</h3>
                <p style="color: #666; margin-bottom: 20px;">
                    Tous les fichiers sont prêts pour le déploiement en production.<br>
                    Application multilingue complète avec 3 langues et support RTL.
                </p>
                <a href="index.html" style="background: #4caf50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px;">
                    🚀 Lancer l'Application
                </a>
            </div>
        </div>
    </div>
</body>
</html>
