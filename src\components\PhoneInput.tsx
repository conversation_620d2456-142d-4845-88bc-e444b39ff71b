import React, { useState } from 'react';

interface PhoneInputProps {
  onVerify: (phoneNumber: string) => void;
  loading: boolean;
}

const PhoneInput: React.FC<PhoneInputProps> = ({ onVerify, loading }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [error, setError] = useState('');

  const validatePhoneNumber = (phone: string): boolean => {
    // Validation pour un numéro avec indicatif sans le + (ex: 2250769989178)
    const phoneRegex = /^[1-9]\d{9,14}$/;
    return phoneRegex.test(phone);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!phoneNumber.trim()) {
      setError('Veuillez saisir un numéro de téléphone');
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      setError('Format invalide. Utilisez le format: 2250769989178 ');
      return;
    }

    // Utiliser directement le numéro saisi
    onVerify(phoneNumber);
  };

  return (
    <div className="phone-input-container">
      <div className="phone-input-card">
        <div style={{ fontSize: '48px', marginBottom: '20px', color: '#1976d2' }}>
          🏥💉
        </div>
        <h2>Vaccination de la fièvre jaune</h2>
        <p>Veuillez saisir votre numéro de téléphone sans l'indicatif</p>
        
        <form onSubmit={handleSubmit} className="phone-form">
          <div className="input-group">
            <label htmlFor="phone">Numéro de téléphone</label>
            <input
              type="tel"
              id="phone"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="Ex: 2250769989178"
              className={error ? 'error' : ''}
              disabled={loading}
            />
            {error && <span className="error-message">{error}</span>}
          </div>
          
          <button 
            type="submit" 
            className="verify-button"
            disabled={loading}
          >
            {loading ? 'Vérification...' : 'Vérifiez'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default PhoneInput;
