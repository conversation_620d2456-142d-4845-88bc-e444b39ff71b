# 🚨 Solution pour l'erreur 500 - Problème .htaccess

## Problème identifié
L'erreur `Invalid command '\xef\xbb\xbf#'` indique que le fichier `.htaccess` contient un BOM (Byte Order Mark) UTF-8 au début du fichier.

## Solutions immédiates

### Solution 1 : Supprimer temporairement .htaccess
```bash
# Sur le serveur, renommez le fichier .htaccess
mv .htaccess .htaccess-backup
```

### Solution 2 : Utiliser la version simple
```bash
# Remplacez .htaccess par la version simple
cp .htaccess-simple .htaccess
```

### Solution 3 : Recréer le fichier sans BOM
1. Supprimez le fichier `.htaccess` existant
2. Créez un nouveau fichier avec un éditeur qui ne ajoute pas de BOM
3. Copiez le contenu de `.htaccess-simple`

## Test de fonctionnement

### Étape 1 : Test sans .htaccess
1. Supprimez/renommez le fichier `.htaccess`
2. Testez l'accès à `index.html`
3. <PERSON> ça marche → Le problème vient du .htaccess

### Étape 2 : Test avec .htaccess simple
1. Utilisez le fichier `.htaccess-simple`
2. Testez à nouveau

### Étape 3 : Diagnostic complet
Utilisez `diagnostic.html` pour tester toutes les fonctionnalités.

## Fichiers de test disponibles
- `test-simple.html` - Test basique
- `diagnostic.html` - Diagnostic complet
- `index-backup.html` - Version simplifiée de l'app
- `.htaccess-simple` - Configuration Apache minimale

## Commandes serveur utiles
```bash
# Vérifier les permissions
ls -la

# Corriger les permissions si nécessaire
chmod 644 *.html *.jpg *.svg
chmod 644 .htaccess
chmod 755 .

# Voir les logs d'erreur Apache
tail -f /var/log/apache2/error.log
```

## Contact
Si le problème persiste, vérifiez les logs du serveur web pour plus de détails.
