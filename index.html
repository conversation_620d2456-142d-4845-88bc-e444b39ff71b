<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carnet de Vaccination Électronique</title>
    <meta name="description" content="Consultation du carnet de vaccination électronique - Vérifiez vos vaccinations en ligne">
    <!-- Favicons officiels -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">

    <!-- Meta tags pour mobile -->
    <meta name="theme-color" content="#4caf50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Carnet Vaccin">
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.15);
            border: 1px solid #e3f2fd;
            text-align: center;
        }
        .icon { font-size: 48px; margin-bottom: 20px; color: #ff9800; }
        h1 { color: #1b5e20; margin-bottom: 10px; font-size: 24px; font-weight: 600; }
        p { color: #e65100; margin-bottom: 30px; font-size: 16px; }
        .form { display: flex; flex-direction: column; gap: 20px; }
        .input-group { text-align: left; }
        label { display: block; margin-bottom: 8px; font-weight: 600; color: #1b5e20; }
        input {
            width: 100%; padding: 12px 16px; border: 2px solid #b0bec5;
            border-radius: 8px; font-size: 16px; background-color: #fafafa;
            transition: border-color 0.3s;
        }
        input:focus { outline: none; border-color: #ff9800; background-color: white; }
        .btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; border: none; padding: 14px 28px; border-radius: 8px;
            font-size: 16px; font-weight: 600; cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; background: #90a4ae; }
        .error { color: #d32f2f; margin-top: 10px; font-weight: 500; }
        .loading { color: #ff9800; margin-top: 10px; }

        /* Styles pour le sélecteur de choix */
        .choice-container {
            margin-bottom: 25px;
            text-align: center;
        }

        .choice-title {
            font-size: 16px;
            font-weight: 600;
            color: #1b5e20;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .choice-title .choice-icon {
            font-size: 20px;
        }

        .choice-options {
            display: inline-flex;
            background: white;
            border-radius: 16px;
            padding: 6px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            border: 2px solid #e3f2fd;
            position: relative;
            overflow: hidden;
        }

        .choice-option {
            background: transparent;
            border: none;
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #546e7a;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
            min-width: 140px;
            justify-content: center;
        }

        .choice-option:hover:not(.selected) {
            color: #37474f;
            background: rgba(255, 152, 0, 0.08);
            transform: translateY(-1px);
        }

        .choice-option.selected {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .choice-option .option-icon {
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .choice-option.selected .option-icon {
            transform: scale(1.15);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
        }

        .choice-option .option-text {
            font-weight: 700;
            letter-spacing: 0.3px;
        }

        /* Indicateur de sélection avec animation */
        .choice-indicator {
            position: absolute;
            top: 6px;
            left: 6px;
            height: calc(100% - 12px);
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            border-radius: 12px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            box-shadow: 0 3px 12px rgba(76, 175, 80, 0.4);
            width: calc(50% - 6px);
        }

        .choice-indicator.name-mode {
            transform: translateX(100%);
        }

        /* Animation de pulsation pour attirer l'attention */
        @keyframes choicePulse {
            0% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12); }
            50% { box-shadow: 0 6px 25px rgba(255, 152, 0, 0.2); }
            100% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12); }
        }

        .choice-options {
            animation: choicePulse 3s ease-in-out infinite;
        }

        .choice-options:hover {
            animation: none;
        }

        /* Badge "OU" entre les options */
        .choice-separator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            color: #088e33;
            font-size: 11px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 20px;
            border: 2px solid #4caf50;
            z-index: 3;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
            letter-spacing: 0.5px;
        }

        .form-section {
            display: none;
            animation: choiceSlideIn 0.5s ease-out;
        }

        .form-section.active {
            display: block;
        }

        @keyframes choiceSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive pour le sélecteur de choix */
        @media (max-width: 480px) {
            .choice-options {
                width: 100%;
                max-width: 340px;
            }

            .choice-option {
                padding: 10px 18px;
                font-size: 13px;
                min-width: 120px;
            }

            .choice-option .option-icon {
                font-size: 16px;
            }

            .choice-separator {
                font-size: 10px;
                padding: 3px 6px;
            }
        }

        /* Styles pour les animations */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Styles pour le sélecteur de langue */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .language-dropdown {
            position: relative;
            display: inline-block;
        }

        .language-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            padding: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
            position: relative;
        }

        .language-button:hover {
            background: #4caf50;
            color: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .language-icon {
            font-size: 20px;
            line-height: 1;
        }

        .language-current {
            display: none;
        }

        .language-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            margin-top: 8px;
        }

        .language-dropdown.open .language-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            font-weight: 500;
        }

        .language-option:last-child {
            border-bottom: none;
        }

        .language-option:hover {
            background: #f1f8e9;
            color: #2e7d32;
        }

        .language-option.active {
            background: #4caf50;
            color: white;
        }

        .language-option .flag {
            font-size: 18px;
        }

        /* Support RTL pour l'arabe */
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .rtl .language-selector {
            left: 20px;
            right: auto;
        }

        .rtl .language-menu {
            left: 0;
            right: auto;
        }

        .rtl .choice-option {
            justify-content: flex-end;
            padding-right: 16px;
            padding-left: 20px;
        }

        .rtl .choice-option .option-icon {
            order: 2;
            text-align: right;
        }

        .rtl .choice-option .option-text {
            text-align: right;
        }

        /* Responsive pour le sélecteur de langue */
        @media (max-width: 768px) {
            .language-selector {
                top: 15px;
                right: 15px;
            }

            .language-button {
                width: 40px;
                height: 40px;
                padding: 8px;
                font-size: 18px;
            }

            .language-menu {
                min-width: 140px;
                right: -10px;
            }
        }

        @media (max-width: 480px) {
            .language-selector {
                top: 12px;
                right: 12px;
            }

            .language-button {
                width: 36px;
                height: 36px;
                padding: 6px;
                font-size: 16px;
            }

            .language-menu {
                min-width: 130px;
                right: -15px;
            }
        }

        /* Media queries pour responsive - GARDE LA DISPOSITION HORIZONTALE */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                padding: 15px;
                margin: 5px;
            }

            /* En-tête responsive - GARDE flex horizontal */
            .header-responsive {
                padding: 10px 8px !important;
            }

            .header-responsive .logo-sante {
                width: 35px !important;
                height: 35px !important;
            }

            .header-responsive .logo-inhp {
                width: 60px !important;
                height: 60px !important;
            }

            .header-responsive h2 {
                font-size: 14px !important;
            }

            .header-responsive p {
                font-size: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                margin: 5px;
            }

            /* En-tête mobile - GARDE la disposition horizontale */
            .header-responsive {
                padding: 8px 5px !important;
            }

            .header-responsive .logo-sante {
                width: 28px !important;
                height: 28px !important;
            }

            .header-responsive .logo-inhp {
                width: 45px !important;
                height: 45px !important;
            }

            .header-responsive h2 {
                font-size: 12px !important;
            }

            .header-responsive p {
                font-size: 8px !important;
            }
        }

        /* Styles responsive pour la carte de vaccination - GARDE LA DISPOSITION HORIZONTALE */
        @media (max-width: 768px) {
            .card-header-responsive {
                padding: 6px 8px !important;
            }

            .card-header-responsive .logo-sante-card {
                width: 32px !important;
                height: 32px !important;
            }

            .card-header-responsive .logo-inhp-card {
                width: 65px !important;
                height: 65px !important;
            }

            .card-header-responsive h2 {
                font-size: 15px !important;
            }

            .card-header-responsive p {
                font-size: 10px !important;
            }
        }

        @media (max-width: 480px) {
            .card-header-responsive {
                padding: 5px 4px !important;
            }

            .card-header-responsive .logo-sante-card {
                width: 28px !important;
                height: 28px !important;
            }

            .card-header-responsive .logo-inhp-card {
                width: 50px !important;
                height: 50px !important;
            }

            .card-header-responsive h2 {
                font-size: 13px !important;
            }

            .card-header-responsive p {
                font-size: 9px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sélecteur de langue -->
        <div class="language-selector">
            <div class="language-dropdown" id="languageDropdown">
                <button class="language-button" onclick="toggleLanguageMenu()" title="Changer de langue / Change language / تغيير اللغة">
                    <span class="language-icon">🌐</span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <div class="language-option active" onclick="changeLanguage('fr')">
                        <span class="flag">🇫🇷</span>
                        <span>Français</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('en')">
                        <span class="flag">🇬🇧</span>
                        <span>English</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('ar')">
                        <span class="flag">🇸🇦</span>
                        <span>العربية</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- En-tête avec les deux logos -->
        <div class="header-responsive" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); border-radius: 10px; color: white;">
            <!-- Logo Santé à gauche -->
            <div style="flex: 0 0 60px;">
                <img src="logo_sante.jpg" alt="Ministère de la Santé" class="logo-sante" style="width: 40px; height: 40px; border-radius: 5px; object-fit: contain;">
            </div>

            <!-- Texte au centre -->
            <div class="text-container" style="flex: 1; text-align: center;">
                <h2 style="margin: 0; font-size: 16px; font-weight: 700;" data-translate="header.title">CARNET DE VACCINATION</h2>
                <p style="margin: 0; font-size: 11px; color: #1b5e20; font-weight: 600;" data-translate="header.country">République de Côte d'Ivoire</p>
            </div>

            <!-- Logo INHP à droite -->
            <div style="flex: 0 0 100px; text-align: right;">
                <img src="logo_inhp.jpg" alt="INHP" class="logo-inhp" style="width: 100px; height: 100px; border-radius: 12px; object-fit: contain;">
            </div>
        </div>

        <h1 data-translate="main.title">Vérification de la vaccination contre la fièvre jaune</h1>

        <!-- Sélecteur de choix de recherche -->
        <div class="choice-container">
            <div class="choice-title">
                <span class="choice-icon">🔍</span>
                <span data-translate="choice.title">Choisissez votre méthode de recherche</span>
            </div>

            <div class="choice-options">
                <div class="choice-indicator" id="choiceIndicator"></div>
                <div class="choice-separator">OU</div>

                <button type="button" class="choice-option selected" id="phoneChoice" onclick="switchSearchMode('phone')">
                    <span class="option-icon">📱</span>
                    <span class="option-text" data-translate="choice.phone">Par téléphone</span>
                </button>

                <button type="button" class="choice-option" id="nameChoice" onclick="switchSearchMode('name')">
                    <span class="option-icon">👤</span>
                    <span class="option-text" data-translate="choice.identity">Par identité</span>
                </button>
            </div>
        </div>

        <p id="mainDescription" data-translate="main.description">Veuillez saisir votre numéro précedé de l'indicatif et cliquez sur le bouton "Vérifiez"</p>

        <form class="form" id="vaccinForm">
            <!-- Section recherche par téléphone -->
            <div class="form-section active" id="phoneSection">
                <div class="input-group">
                    <label for="phone" data-translate="form.phone.label">Numéro de téléphone</label>
                    <input type="tel" id="phone" data-translate-placeholder="form.phone.placeholder" placeholder="Ex: 2250769989178">
                </div>
            </div>

            <!-- Section recherche par nom/prénom/date de naissance -->
            <div class="form-section" id="nameSection">
                <div class="input-group">
                    <label for="lastName" data-translate="form.lastName.label">Nom de famille</label>
                    <input type="text" id="lastName" data-translate-placeholder="form.lastName.placeholder" placeholder="Ex: KOUAME">
                </div>
                <div class="input-group">
                    <label for="firstName" data-translate="form.firstName.label">Prénom</label>
                    <input type="text" id="firstName" data-translate-placeholder="form.firstName.placeholder" placeholder="Ex: Jean">
                </div>
                <div class="input-group">
                    <label for="birthDate" data-translate="form.birthDate.label">Date de naissance</label>
                    <input type="date" id="birthDate" max="2010-12-31" min="1900-01-01" data-translate-placeholder="form.birthDate.placeholder" placeholder="jj/mm/aaaa">
                </div>
            </div>

            <button type="submit" class="btn" id="submitBtn" data-translate="form.submit">Vérifiez</button>
        </form>
        
        <div id="message"></div>
    </div>

    <!-- Résultats en dehors du container pour être visibles -->
    <div id="results"></div>

    <script>
        // Variables globales
        let vaccinationData = [];
        let lastSearchedNumber = '';
        let currentSearchMode = 'phone'; // 'phone' ou 'name'
        let currentLanguage = 'fr'; // Langue par défaut

        // Traductions
        const translations = {
            fr: {
                'header.title': 'CARNET DE VACCINATION',
                'header.country': 'République de Côte d\'Ivoire',
                'main.title': 'Vérification de la vaccination contre la fièvre jaune',
                'choice.title': 'Choisissez votre méthode de recherche',
                'choice.phone': 'Par téléphone',
                'choice.identity': 'Par identité',
                'main.description': 'Veuillez saisir votre numéro précedé de l\'indicatif et cliquez sur le bouton "Vérifiez"',
                'main.description.identity': 'Veuillez saisir votre nom, prénom et date de naissance puis cliquez sur "Vérifiez"',
                'form.phone.label': 'Numéro de téléphone',
                'form.phone.placeholder': 'Ex: 2250769989178',
                'form.lastName.label': 'Nom de famille',
                'form.lastName.placeholder': 'Ex: KOUAME',
                'form.firstName.label': 'Prénom',
                'form.firstName.placeholder': 'Ex: Jean',
                'form.birthDate.label': 'Date de naissance',
                'form.birthDate.placeholder': 'jj/mm/aaaa',
                'form.submit': 'Vérifiez',
                'error.phone.required': 'Veuillez saisir un numéro de téléphone',
                'error.phone.format': 'Format invalide. Utilisez le format: 2250769989178 (sans le +)',
                'error.identity.required': 'Veuillez remplir tous les champs (nom, prénom et date de naissance)',
                'error.not.found': 'Carnet non trouvé',
                'error.connection': 'Erreur de connexion au serveur. Veuillez réessayer.',
                'loading': 'Vérification...'
            },
            en: {
                'header.title': 'ELECTRONIC VACCINATION CARD',
                'header.country': 'Republic of Côte d\'Ivoire',
                'main.title': 'Yellow fever vaccination verification',
                'choice.title': 'Choose your search method',
                'choice.phone': 'By phone',
                'choice.identity': 'By identity',
                'main.description': 'Please enter your number with country code and click "Verify"',
                'main.description.identity': 'Please enter your last name, first name and date of birth then click "Verify"',
                'form.phone.label': 'Phone number',
                'form.phone.placeholder': 'Ex: 2250769989178',
                'form.lastName.label': 'Last name',
                'form.lastName.placeholder': 'Ex: KOUAME',
                'form.firstName.label': 'First name',
                'form.firstName.placeholder': 'Ex: Jean',
                'form.birthDate.label': 'Date of birth',
                'form.birthDate.placeholder': 'dd/mm/yyyy',
                'form.submit': 'Verify',
                'error.phone.required': 'Please enter a phone number',
                'error.phone.format': 'Invalid format. Use format: 2250769989178 (without +)',
                'error.identity.required': 'Please fill in all fields (last name, first name and date of birth)',
                'error.not.found': 'Card not found',
                'error.connection': 'Server connection error. Please try again.',
                'loading': 'Verifying...'
            },
            ar: {
                'header.title': 'بطاقة التطعيم الإلكترونية',
                'header.country': 'جمهورية كوت ديفوار',
                'main.title': 'التحقق من تطعيم الحمى الصفراء',
                'choice.title': 'اختر طريقة البحث',
                'choice.phone': 'بالهاتف',
                'choice.identity': 'بالهوية',
                'main.description': 'يرجى إدخال رقمك مع رمز البلد والنقر على "تحقق"',
                'main.description.identity': 'يرجى إدخال اسم العائلة والاسم الأول وتاريخ الميلاد ثم النقر على "تحقق"',
                'form.phone.label': 'رقم الهاتف',
                'form.phone.placeholder': 'مثال: 2250769989178',
                'form.lastName.label': 'اسم العائلة',
                'form.lastName.placeholder': 'مثال: كوامي',
                'form.firstName.label': 'الاسم الأول',
                'form.firstName.placeholder': 'مثال: جان',
                'form.birthDate.label': 'تاريخ الميلاد',
                'form.birthDate.placeholder': 'يوم/شهر/سنة',
                'form.submit': 'تحقق',
                'error.phone.required': 'يرجى إدخال رقم الهاتف',
                'error.phone.format': 'تنسيق غير صحيح. استخدم التنسيق: 2250769989178 (بدون +)',
                'error.identity.required': 'يرجى ملء جميع الحقول (اسم العائلة والاسم الأول وتاريخ الميلاد)',
                'error.not.found': 'البطاقة غير موجودة',
                'error.connection': 'خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
                'loading': 'جاري التحقق...'
            }
        };

        // Fonctions de gestion des langues
        function toggleLanguageMenu() {
            const dropdown = document.getElementById('languageDropdown');
            dropdown.classList.toggle('open');
        }

        function changeLanguage(lang) {
            currentLanguage = lang;

            // Mettre à jour les options actives
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeLanguage('${lang}')"]`).classList.add('active');

            // Appliquer les traductions
            applyTranslations(lang);

            // Gérer RTL pour l'arabe
            if (lang === 'ar') {
                document.body.classList.add('rtl');
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.body.classList.remove('rtl');
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', lang);
            }

            // Fermer le menu
            document.getElementById('languageDropdown').classList.remove('open');

            // Sauvegarder la préférence
            localStorage.setItem('selectedLanguage', lang);
        }

        function applyTranslations(lang) {
            const texts = translations[lang];

            // Traduire tous les éléments avec data-translate
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (texts[key]) {
                    element.textContent = texts[key];
                }
            });

            // Traduire les placeholders
            document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
                const key = element.getAttribute('data-translate-placeholder');
                if (texts[key]) {
                    element.placeholder = texts[key];
                }
            });

            // Mettre à jour la description selon le mode actuel
            updateDescription();
        }

        function updateDescription() {
            const mainDescription = document.getElementById('mainDescription');
            const key = currentSearchMode === 'phone' ? 'main.description' : 'main.description.identity';
            const text = translations[currentLanguage][key];
            if (text) {
                mainDescription.textContent = text;
            }
        }

        function getTranslatedText(key) {
            return translations[currentLanguage][key] || key;
        }

        // Fermer le menu de langue en cliquant ailleurs
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageDropdown');
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });

        // Attendre que le DOM soit chargé
        document.addEventListener('DOMContentLoaded', function() {
            // Charger la langue sauvegardée
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'fr';
            if (savedLanguage !== 'fr') {
                changeLanguage(savedLanguage);
            }

            initializeApp();
        });

        // Fonction pour basculer entre les modes de recherche
        function switchSearchMode(mode) {
            currentSearchMode = mode;

            // Mettre à jour les choix
            const phoneChoice = document.getElementById('phoneChoice');
            const nameChoice = document.getElementById('nameChoice');
            const phoneSection = document.getElementById('phoneSection');
            const nameSection = document.getElementById('nameSection');
            const mainDescription = document.getElementById('mainDescription');
            const choiceIndicator = document.getElementById('choiceIndicator');

            if (mode === 'phone') {
                phoneChoice.classList.add('selected');
                nameChoice.classList.remove('selected');
                phoneSection.classList.add('active');
                nameSection.classList.remove('active');
                choiceIndicator.classList.remove('name-mode');
            } else {
                phoneChoice.classList.remove('selected');
                nameChoice.classList.add('selected');
                phoneSection.classList.remove('active');
                nameSection.classList.add('active');
                choiceIndicator.classList.add('name-mode');
            }

            // Mettre à jour la description selon la langue actuelle
            updateDescription();

            // Effacer les messages d'erreur précédents
            const messageDiv = document.getElementById('message');
            if (messageDiv) {
                messageDiv.innerHTML = '';
            }
        }

        function initializeApp() {
            // Vérifier s'il faut restaurer les résultats
            const shouldShowResults = localStorage.getItem('showResultsOnLoad');
            if (shouldShowResults === 'true') {
                const savedData = localStorage.getItem('vaccinationData');
                const savedNumber = localStorage.getItem('lastSearchedNumber');

                if (savedData) {
                    vaccinationData = JSON.parse(savedData);
                    lastSearchedNumber = savedNumber;

                    // Nettoyer le localStorage
                    localStorage.removeItem('showResultsOnLoad');
                    localStorage.removeItem('vaccinationData');
                    localStorage.removeItem('lastSearchedNumber');

                    // Afficher les résultats
                    showResults(vaccinationData);
                    return;
                }
            }

            const form = document.getElementById('vaccinForm');
            const phoneInput = document.getElementById('phone');
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');

            if (!form || !phoneInput || !submitBtn || !messageDiv || !resultsDiv) {
                return;
            }

            // Ajouter l'événement au formulaire
            form.addEventListener('submit', handleFormSubmit);
        }

        async function handleFormSubmit(e) {
            e.preventDefault();

            if (currentSearchMode === 'phone') {
                await handlePhoneSearch();
            } else {
                await handleNameSearch();
            }
        }

        async function handlePhoneSearch() {
            const phoneInput = document.getElementById('phone');
            const phoneNumber = phoneInput.value.trim();

            if (!phoneNumber) {
                showMessage(getTranslatedText('error.phone.required'), 'error');
                return;
            }

            if (!/^[1-9]\d{9,14}$/.test(phoneNumber)) {
                showMessage(getTranslatedText('error.phone.format'), 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tel: phoneNumber, d: 'PROD' })
                });

                const data = await response.json();

                if (data.code === 0 && data.data) {
                    // Sauvegarder le numéro recherché
                    lastSearchedNumber = phoneNumber;

                    if (data.data.length === 1) {
                        // Si un seul résultat, afficher directement les détails
                        vaccinationData = data.data;
                        showDetails(0, true);
                    } else {
                        // Si plusieurs résultats, afficher le tableau
                        showResults(data.data);
                    }
                } else {
                    showMessage(data.msg || getTranslatedText('error.not.found'), 'error');
                }
            } catch (error) {
                showMessage(getTranslatedText('error.connection'), 'error');
            } finally {
                setLoading(false);
            }
        }

        async function handleNameSearch() {
            const lastNameInput = document.getElementById('lastName');
            const firstNameInput = document.getElementById('firstName');
            const birthDateInput = document.getElementById('birthDate');

            const lastName = lastNameInput.value.trim().toUpperCase();
            const firstName = firstNameInput.value.trim().toUpperCase();
            const birthDate = birthDateInput.value;

            if (!lastName || !firstName || !birthDate) {
                showMessage(getTranslatedText('error.identity.required'), 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcodeplus', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        d: 'PROD',
                        nompat: lastName,
                        prenompat: firstName,
                        datenaissancepat: birthDate
                    })
                });

                const data = await response.json();

                if (data.code === 0 && data.data) {
                    if (data.data.length === 1) {
                        // Si un seul résultat, afficher directement les détails
                        vaccinationData = data.data;
                        showDetails(0, true);
                    } else {
                        // Si plusieurs résultats, afficher le tableau
                        showResults(data.data);
                    }
                } else {
                    showMessage(data.msg || getTranslatedText('error.not.found'), 'error');
                }
            } catch (error) {
                showMessage(getTranslatedText('error.connection'), 'error');
            } finally {
                setLoading(false);
            }
        }



        function setLoading(loading) {
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.disabled = loading;
                submitBtn.textContent = loading ? getTranslatedText('loading') : getTranslatedText('form.submit');
            }
            if (loading) {
                showMessage(getTranslatedText('loading'), 'loading');
            }
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            if (messageDiv) {
                messageDiv.innerHTML = `<div class="${type}">${text}</div>`;
            }
        }

        function showResults(records) {
            const messageDiv = document.getElementById('message');
            const resultsDiv = document.getElementById('results');
            const container = document.querySelector('.container');

            if (messageDiv) messageDiv.innerHTML = '';
            if (container) {
                container.style.display = 'none';
            }

            const html = `
                <div style="max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; padding: 30px; box-shadow: 0 10px 30px rgba(27, 94, 32, 0.15); border: 1px solid #c8e6c9;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; flex-wrap: wrap; gap: 15px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <span style="font-size: 32px; color: #ff9800;">📋💉</span>
                            <div>
                                <h2 style="color: #1b5e20; margin: 0; font-weight: 600;">Carnet de Vaccination Électronique</h2>
                                <p style="color: #e65100; margin: 0; font-weight: 500;">${records.length} vaccination(s) trouvée(s)</p>
                            </div>
                        </div>
                        <button onclick="location.reload()" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px; box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);">
                            Nouvelle recherche
                        </button>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%);">
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Patient</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Type de Vaccin</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Date de Vaccination</th>
                                    <th style="padding: 15px; text-align: left; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Centre</th>
                                    <th style="padding: 15px; text-align: center; font-weight: 600; color: #1b5e20; border-bottom: 2px solid #ff9800;">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${records.map((record, index) => `
                                    <tr style="transition: background-color 0.3s ease;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor='white'">
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            <div>
                                                <div style="font-weight: 600; color: #1b5e20;">${record.NOMPAT} ${record.PRENOMPAT}</div>
                                                <div style="font-size: 14px; color: #e65100;">+${record.NUMEROPAT}</div>
                                            </div>
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            <span style="background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; box-shadow: 0 2px 4px rgba(27, 94, 32, 0.3);">
                                                ${record.NOMVAC}
                                            </span>
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            ${new Date(record.PRESENCE).toLocaleDateString('fr-FR')}
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle;">
                                            ${record.NOMCENTR}
                                        </td>
                                        <td style="padding: 15px; border-bottom: 1px solid #eee; vertical-align: middle; text-align: center;">
                                            <button onclick="showDetails(${index})" style="
                                                background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
                                                color: white; border: none; padding: 10px 16px; border-radius: 8px;
                                                cursor: pointer; font-size: 14px; font-weight: 600;
                                                transition: all 0.3s ease; box-shadow: 0 3px 6px rgba(255, 152, 0, 0.3);
                                                display: flex; align-items: center; gap: 6px; min-width: 100px;
                                                justify-content: center; margin: 0 auto;
                                            " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 12px rgba(255, 152, 0, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 3px 6px rgba(255, 152, 0, 0.3)'">
                                                <span style="font-size: 16px;">👁️</span>
                                                <span style="font-size: 13px; letter-spacing: 0.5px;">Détails</span>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
            if (resultsDiv) {
                resultsDiv.innerHTML = html;
            }
            vaccinationData = records; // Stocker les données pour la vue détaillée
        }

        function showDetails(index, fromDirectCall = false) {
            const record = vaccinationData[index];

            if (!record) {
                return;
            }

            // Cacher le formulaire et les résultats
            const container = document.querySelector('.container');
            const resultsDiv = document.getElementById('results');

            if (container) container.style.display = 'none';
            if (resultsDiv) resultsDiv.style.display = 'none';

            const formatDate = (dateString) => {
                const date = new Date(dateString);
                return date.toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: 'numeric',
                    day: 'numeric'
                });
            };

            const calculateAge = (birthDate) => {
                const birth = new Date(birthDate);
                const today = new Date();
                let age = today.getFullYear() - birth.getFullYear();
                const monthDiff = today.getMonth() - birth.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--;
                }

                return age;
            };

            const detailsHtml = `
                <div style="min-height: 100vh; background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%); padding: 20px; display: flex; align-items: center; justify-content: center;">
                    <div class="fade-in" style="width: 100%; max-width: 450px;">
                        <!-- Boutons de navigation flottants -->
                        <div style="text-align: center; margin-bottom: 20px;">
                            ${vaccinationData.length > 1 ? `
                                <button onclick="goBackToResults()" style="background: rgba(27, 94, 32, 0.9); color: white; border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 600; margin-right: 10px; box-shadow: 0 4px 12px rgba(27, 94, 32, 0.3); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                    ← Retour aux résultats
                                </button>
                            ` : ''}
                            <button onclick="location.reload()" style="background: rgba(255, 152, 0, 0.9); color: white; border: none; padding: 10px 20px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 600; box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                                🔍 Nouvelle recherche
                            </button>
                        </div>

                        <!-- Carte d'identité vaccination -->
                        <div style="background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 8px 25px rgba(27, 94, 32, 0.15); border: 1px solid #c8e6c9; position: relative;">
                            <!-- En-tête de la carte -->
                            <div class="card-header-responsive" style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 8px 15px; text-align: center; position: relative;">
                                <div style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                    <!-- Logo Santé à gauche -->
                                    <div style="flex: 0 0 80px;">
                                        <img src="logo_sante.jpg" alt="Ministère de la Santé" class="logo-sante-card" style="width: 40px; height: 40px; border-radius: 5px; object-fit: contain;">
                                    </div>

                                    <!-- Texte au centre, aligné avec les logos -->
                                    <div style="flex: 1; transform: translateY(8px);">
                                        <h2 style="margin: 0; font-size: 18px; font-weight: 700;">CARNET DE VACCINATION</h2>
                                        <p style="margin: 0; font-size: 12px; color: #1b5e20; font-weight: 600;">République de Côte d'Ivoire</p>
                                    </div>

                                    <!-- Logo INHP à droite -->
                                    <div style="flex: 0 0 90px;">
                                        <img src="logo_inhp.jpg" alt="INHP" class="logo-inhp-card" style="width: 90px; height: 90px; border-radius: 12px; object-fit: contain;">
                                    </div>
                                </div>
                            </div>

                            <!-- Corps de la carte -->
                            <div style="padding: 20px;">
                                <!-- Section patient -->
                                <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                                    <!-- Photo -->
                                    <div style="flex-shrink: 0;">
                                        ${record.PHOTOPAT ? `
                                            <img
                                                src="https://opisms.net/ecarnet/upload/photo/${record.PHOTOPAT}"
                                                alt="Photo du patient"
                                                style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; border: 2px solid #1b5e20; box-shadow: 0 3px 8px rgba(27, 94, 32, 0.2);"
                                                onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\"color: #546e7a; padding: 25px 15px; border: 2px dashed #b0bec5; border-radius: 8px; background: #f9f9f9; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 24px;\\">
                                                    
                                        ` : `
                                            <div style="color: #1b5e20; padding: 25px 15px; border: 2px dashed #4caf50; border-radius: 8px; background: #f1f8e9; width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; text-align: center; font-size: 24px;">
                                                📷
                                            </div>
                                        `}
                                    </div>

                                    <!-- Informations patient -->
                                    <div style="flex: 1; min-width: 0;">
                                        <h3 style="margin: 0 0 8px 0; color: #1b5e20; font-size: 18px; font-weight: 700; text-transform: uppercase; letter-spacing: 0.5px;">
                                            ${record.NOMPAT} ${record.PRENOMPAT}
                                        </h3>
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 13px;">
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Sexe:</span><br>
                                                <span style="color: #37474f;">${record.SEXEPAT === 'M' ? '👨 Masculin' : '👩 Féminin'}</span>
                                            </div>
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Né(e) le:</span><br>
                                                <span style="color: #37474f;">${formatDate(record.DATEPAT)}</span>
                                            </div>
                                            <div>
                                                <span style="color: #546e7a; font-weight: 600;">Téléphone:</span><br>
                                                <span style="color: #37474f;">📱 +${record.NUMEROPAT}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>




                                <!-- Section vaccination -->
                                <div style="border-top: 2px solid #e3f2fd; padding-top: 15px;">
                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <div style="display: inline-flex; align-items: center; gap: 8px; background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 700; box-shadow: 0 3px 8px rgba(27, 94, 32, 0.3);">
                                            <span style="font-size: 16px;">🛡️</span>
                                            Vaccin ${record.NOMVAC.toLowerCase()} effectué
                                        </div>
                                    </div>

                                    <div style="background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%); border-radius: 10px; padding: 15px; border: 1px solid #4caf50;">
                                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 13px;">
                                            <div>
                                                <span style="color: #e65100; font-weight: 600;">📅 Date:</span><br>
                                                <span style="color: #1b5e20; font-weight: 600;">${formatDate(record.PRESENCE)}</span>
                                            </div>
                                            <div>
                                                <span style="color: #e65100; font-weight: 600;">🏷️ Lot N°:</span><br>
                                                <span style="color: #1b5e20; font-weight: 600;">${record.LOVAC}</span>
                                            </div>
                                        </div>
                                        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #4caf50;">
                                            <span style="color: #e65100; font-weight: 600; font-size: 12px;">🏥 Centre de vaccination:</span><br>
                                            <span style="color: #1b5e20; font-weight: 600; font-size: 13px;">${record.NOMCENTR}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pied de carte -->
                                <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #e0e0e0; text-align: center;">
                                    <div style="font-size: 11px; color: #999; line-height: 1.3;">
                                        <strong>Le vaccin de fièvre jaune effectué</strong><br>
                                        Carnet de vaccination électronique certifié
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remplacer le contenu de la page
            document.body.innerHTML = detailsHtml;
        }

        function goBackToResults() {
            // Sauvegarder les données dans le localStorage
            localStorage.setItem('vaccinationData', JSON.stringify(vaccinationData));
            localStorage.setItem('lastSearchedNumber', lastSearchedNumber);
            localStorage.setItem('showResultsOnLoad', 'true');

            // Recharger la page
            location.reload();
        }




    </script>
</body>
</html>