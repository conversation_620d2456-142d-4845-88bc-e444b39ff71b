﻿<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Production - Carnet de Vaccination</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%);
            min-height: 100vh; margin: 0; display: flex; align-items: center; justify-content: center;
        }
        .container {
            max-width: 700px; background: white; padding: 40px; border-radius: 15px;
            box-shadow: 0 8px 25px rgba(27, 94, 32, 0.15); text-align: center; border: 1px solid #c8e6c9;
        }
        .header { 
            display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 30px;
            padding: 15px; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            border-radius: 10px; color: white;
        }
        .logo { width: 60px; height: 60px; border-radius: 8px; object-fit: contain; }
        .success { color: #1b5e20; font-weight: bold; font-size: 28px; margin-bottom: 20px; }
        .info { color: #e65100; margin-bottom: 15px; font-size: 16px; }
        .features { text-align: left; margin: 20px 0; }
        .feature { margin: 10px 0; color: #1b5e20; }
        .link { 
            display: inline-block; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; text-decoration: none; padding: 15px 30px; border-radius: 10px;
            font-weight: 600; margin: 10px; transition: transform 0.2s;
        }
        .link:hover { transform: translateY(-2px); }
        .stats { background: #f1f8e9; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="logo_sante.jpg" alt="MinistÃ¨re SantÃ©" class="logo">
            <div>
                <h2 style="margin: 0; font-size: 18px;">CARNET DE VACCINATION</h2>
                <p style="margin: 0; font-size: 12px; color: #1b5e20;">RÃ©publique de CÃ´te d'Ivoire</p>
            </div>
            <img src="logo_inhp.jpg" alt="INHP" class="logo">
        </div>
        
        <div class="success">âœ… Production dÃ©ployÃ©e avec succÃ¨s !</div>
        
        <div class="info"><strong>Application Carnet de Vaccination Ã‰lectronique</strong></div>
        <div class="info">Version: Drapeau Ivoirien v2.0 Final</div>
        
        <div class="stats">
            <strong>Statistiques du build:</strong><br>
            ðŸ“… Date: <span id="timestamp"></span><br>
            ðŸŽ¨ Design: Responsive avec logos harmonisÃ©s<br>
            ðŸ”§ Assets: Tous les logos chargÃ©s avec succÃ¨s<br>
            ðŸŒ CompatibilitÃ©: Desktop, tablette, mobile
        </div>
        
        <div class="features">
            <strong>âœ¨ FonctionnalitÃ©s incluses:</strong>
            <div class="feature">ðŸŽ¨ Design drapeau ivoirien (orange/blanc/vert)</div>
            <div class="feature">ðŸ“± Interface responsive horizontale</div>
            <div class="feature">ðŸ¥ Logos MinistÃ¨re SantÃ© + INHP intÃ©grÃ©s</div>
            <div class="feature">ðŸ” Recherche par numÃ©ro de tÃ©lÃ©phone</div>
            <div class="feature">ðŸ“‹ Affichage intelligent (1 rÃ©sultat = carte directe)</div>
            <div class="feature">ðŸ›¡ï¸ Navigation optimisÃ©e et sÃ©curisÃ©e</div>
        </div>
        
        <a href="index.html" class="link">ðŸš€ AccÃ©der Ã  l'application</a>
        <a href="https://opisms.net/check/" class="link">ðŸŒ Version en ligne</a>
    </div>
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('fr-FR');
    </script>
</body>
</html>
