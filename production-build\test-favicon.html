<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Favicon - Carnet de Vaccination</title>
    
    <!-- Favicons officiels -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="manifest" href="site.webmanifest">
    
    <!-- Meta tags pour mobile -->
    <meta name="theme-color" content="#4caf50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Carnet Vaccin">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1b5e20;
            text-align: center;
            margin-bottom: 30px;
        }
        .favicon-test {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        .favicon-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            width: 32px;
            height: 32px;
            margin-right: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f5f5f5;
        }
        .favicon-info {
            flex: 1;
        }
        .favicon-name {
            font-weight: 600;
            color: #1b5e20;
        }
        .favicon-details {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        .instructions ul {
            margin-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test des Favicons Officiels</h1>
        
        <div class="instructions">
            <h3>📋 Instructions de Test</h3>
            <ul>
                <li><strong>Onglet du navigateur :</strong> Vérifiez que l'icône officielle apparaît dans l'onglet</li>
                <li><strong>Favoris :</strong> Ajoutez cette page aux favoris et vérifiez l'icône</li>
                <li><strong>Mobile :</strong> Ajoutez à l'écran d'accueil sur mobile</li>
                <li><strong>PWA :</strong> Testez l'installation comme application web</li>
            </ul>
        </div>
        
        <div class="favicon-test">
            <h3>🖼️ Favicons Chargés</h3>
            
            <div class="favicon-item">
                <img src="favicon.ico" alt="Favicon ICO" class="favicon-preview" id="favicon-ico">
                <div class="favicon-info">
                    <div class="favicon-name">favicon.ico</div>
                    <div class="favicon-details">Favicon principal - Format ICO</div>
                </div>
                <span class="status" id="status-ico">Test...</span>
            </div>
            
            <div class="favicon-item">
                <img src="favicon-32x32.png" alt="Favicon 32x32" class="favicon-preview" id="favicon-32">
                <div class="favicon-info">
                    <div class="favicon-name">favicon-32x32.png</div>
                    <div class="favicon-details">Favicon 32x32 pixels - Format PNG</div>
                </div>
                <span class="status" id="status-32">Test...</span>
            </div>
            
            <div class="favicon-item">
                <img src="favicon-16x16.png" alt="Favicon 16x16" class="favicon-preview" id="favicon-16">
                <div class="favicon-info">
                    <div class="favicon-name">favicon-16x16.png</div>
                    <div class="favicon-details">Favicon 16x16 pixels - Format PNG</div>
                </div>
                <span class="status" id="status-16">Test...</span>
            </div>
            
            <div class="favicon-item">
                <img src="apple-touch-icon.png" alt="Apple Touch Icon" class="favicon-preview" id="apple-icon">
                <div class="favicon-info">
                    <div class="favicon-name">apple-touch-icon.png</div>
                    <div class="favicon-details">Icône Apple Touch 180x180 pixels</div>
                </div>
                <span class="status" id="status-apple">Test...</span>
            </div>
        </div>
        
        <div class="favicon-test">
            <h3>📱 Configuration PWA</h3>
            <p><strong>Nom de l'app :</strong> Carnet de Vaccination Électronique</p>
            <p><strong>Nom court :</strong> Carnet Vaccin</p>
            <p><strong>Couleur du thème :</strong> <span style="color: #4caf50; font-weight: bold;">#4caf50 (Vert)</span></p>
            <p><strong>Couleur de fond :</strong> <span style="color: #e8f4fd; font-weight: bold;">#e8f4fd (Bleu clair)</span></p>
            <p><strong>Manifest :</strong> <a href="site.webmanifest" target="_blank">site.webmanifest</a></p>
        </div>
        
        <div class="instructions">
            <h3>✅ Vérifications</h3>
            <ul>
                <li>Toutes les images ci-dessus doivent se charger correctement</li>
                <li>L'onglet du navigateur doit afficher l'icône officielle</li>
                <li>Sur mobile, la barre d'état doit être verte (#4caf50)</li>
                <li>L'application peut être installée comme PWA</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="background: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                🏠 Retour à l'Application
            </a>
        </div>
    </div>
    
    <script>
        // Test du chargement des favicons
        document.addEventListener('DOMContentLoaded', function() {
            const favicons = [
                { id: 'favicon-ico', status: 'status-ico', src: 'favicon.ico' },
                { id: 'favicon-32', status: 'status-32', src: 'favicon-32x32.png' },
                { id: 'favicon-16', status: 'status-16', src: 'favicon-16x16.png' },
                { id: 'apple-icon', status: 'status-apple', src: 'apple-touch-icon.png' }
            ];
            
            favicons.forEach(favicon => {
                const img = document.getElementById(favicon.id);
                const status = document.getElementById(favicon.status);
                
                img.onload = function() {
                    status.textContent = '✅ OK';
                    status.className = 'status success';
                };
                
                img.onerror = function() {
                    status.textContent = '❌ Erreur';
                    status.className = 'status error';
                };
            });
            
            // Test du manifest
            if ('serviceWorker' in navigator) {
                console.log('PWA supporté');
            }
        });
    </script>
</body>
</html>
