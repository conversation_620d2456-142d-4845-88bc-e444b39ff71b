import axios from 'axios';

// Types définis directement dans le fichier API pour éviter les problèmes d'importation
export interface VaccinationRecord {
  IDCAL: string;
  NOMVAC: string;
  DATERAPEL: string;
  PRESENCE: string;
  LOVAC: string;
  NOMCENTR: string;
  IDPAT: string;
  NOMPAT: string;
  PRENOMPAT: string;
  NUMEROPAT: string;
  NUMEROPAT2: string;
  DATEPAT: string;
  SEXEPAT: string;
  PHOTOPAT: string; 
}

export interface ApiResponse {
  code: number;
  msg: string;
  data?: VaccinationRecord[];
}

interface ApiRequest {
  tel: string;
  d: string;
}

const API_BASE_URL = 'https://opisms.net/opisms-ws/api/v1/user';

export const vaccinationApi = {
  async getVaccinationRecords(phoneNumber: string): Promise<ApiResponse> {
    try {
      const requestData: ApiRequest = {
        tel: phoneNumber,
        d: 'PROD'
      };

      const response = await axios.post<ApiResponse>(
        `${API_BASE_URL}/ecarnetqrcode`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'appel API:', error);
      throw new Error('Erreur de connexion au serveur');
    }
  }
};
