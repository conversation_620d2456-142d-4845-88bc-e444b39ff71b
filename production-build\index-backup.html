<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carnet de Vaccination Électronique</title>
    <meta name="description" content="Consultation du carnet de vaccination électronique - Vérifiez vos vaccinations en ligne">
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">

    <!-- Meta tags pour mobile -->
    <meta name="theme-color" content="#4caf50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Carnet Vaccin">
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.15);
            border: 1px solid #e3f2fd;
            text-align: center;
            position: relative;
        }

        /* Styles pour le sélecteur de langue */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .language-dropdown {
            position: relative;
            display: inline-block;
        }

        .language-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            padding: 8px;
            cursor: pointer;
            font-size: 16px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }

        .language-button:hover {
            background: #4caf50;
            color: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .language-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 140px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            margin-top: 5px;
        }

        .language-dropdown.open .language-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
            font-weight: 500;
        }

        .language-option:last-child {
            border-bottom: none;
        }

        .language-option:hover {
            background: #f1f8e9;
            color: #2e7d32;
        }

        .language-option.active {
            background: #4caf50;
            color: white;
        }

        .language-option .flag {
            font-size: 16px;
        }

        /* Support RTL pour l'arabe */
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .rtl .language-selector {
            left: 20px;
            right: auto;
        }

        .rtl .language-menu {
            left: 0;
            right: auto;
        }
        h1 { color: #1b5e20; margin-bottom: 20px; font-size: 24px; font-weight: 600; }
        p { color: #e65100; margin-bottom: 30px; font-size: 16px; }
        .form { display: flex; flex-direction: column; gap: 20px; }
        .input-group { text-align: left; }
        label { display: block; margin-bottom: 8px; font-weight: 600; color: #1b5e20; }
        input {
            width: 100%; padding: 12px 16px; border: 2px solid #b0bec5;
            border-radius: 8px; font-size: 16px; background-color: #fafafa;
            transition: border-color 0.3s;
        }
        input:focus { outline: none; border-color: #ff9800; background-color: white; }
        .btn {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; border: none; padding: 14px 28px; border-radius: 8px;
            font-size: 16px; font-weight: 600; cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; background: #90a4ae; }
        .error { color: #d32f2f; margin-top: 10px; font-weight: 500; }
        .loading { color: #ff9800; margin-top: 10px; }
        
        /* Styles pour le sélecteur de choix */
        .choice-container {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .choice-title {
            font-size: 16px;
            font-weight: 600;
            color: #1b5e20;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .choice-options {
            display: inline-flex;
            background: white;
            border-radius: 16px;
            padding: 6px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
            border: 2px solid #e3f2fd;
            position: relative;
            overflow: hidden;
        }
        
        .choice-option {
            background: transparent;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #546e7a;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 12px;
            white-space: nowrap;
            min-width: 150px;
            justify-content: flex-start;
            padding-left: 16px;
        }
        
        .choice-option:hover:not(.selected) {
            color: #37474f;
            background: rgba(255, 152, 0, 0.08);
        }
        
        .choice-option.selected {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .choice-indicator {
            position: absolute;
            top: 6px;
            left: 6px;
            height: calc(100% - 12px);
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            border-radius: 12px;
            transition: all 0.4s ease;
            z-index: 1;
            box-shadow: 0 3px 12px rgba(76, 175, 80, 0.4);
            width: calc(50% - 6px);
        }
        
        .choice-indicator.name-mode {
            transform: translateX(100%);
        }
        
        .choice-separator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            color: #4caf50;
            font-size: 11px;
            font-weight: 700;
            padding: 4px 8px;
            border-radius: 20px;
            border: 2px solid #4caf50;
            z-index: 3;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }
        
        .form-section {
            display: none;
        }
        
        .form-section.active {
            display: block;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .choice-options {
                width: 100%;
                max-width: 340px;
            }
            
            .choice-option {
                padding: 10px 14px;
                font-size: 13px;
                min-width: 130px;
                padding-left: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sélecteur de langue -->
        <div class="language-selector">
            <div class="language-dropdown" id="languageDropdown">
                <button class="language-button" onclick="toggleLanguageMenu()" title="Changer de langue / Change language / تغيير اللغة">
                    <span class="language-icon">🌐</span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <div class="language-option active" onclick="changeLanguage('fr')">
                        <span class="flag">🇫🇷</span>
                        <span>Français</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('en')">
                        <span class="flag">🇬🇧</span>
                        <span>English</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('ar')">
                        <span class="flag">🇸🇦</span>
                        <span>العربية</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- En-tête simple -->
        <div style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
            <h2 style="margin: 0; font-size: 18px; text-align: center;" data-translate="header.title">CARNET DE VACCINATION ÉLECTRONIQUE</h2>
            <p style="margin: 5px 0 0 0; font-size: 12px; text-align: center; color: #1b5e20;" data-translate="header.country">République de Côte d'Ivoire</p>
        </div>

        <h1 data-translate="main.title">Vérification de la vaccination contre la fièvre jaune</h1>
        
        <!-- Sélecteur de choix -->
        <div class="choice-container">
            <div class="choice-title">
                <span>🔍</span>
                <span data-translate="choice.title">Choisissez votre méthode de recherche</span>
            </div>

            <div class="choice-options">
                <div class="choice-indicator" id="choiceIndicator"></div>
                <div class="choice-separator" data-translate="choice.or">OU</div>

                <button type="button" class="choice-option selected" id="phoneChoice" onclick="switchSearchMode('phone')">
                    <span>📱</span>
                    <span data-translate="choice.phone">Par téléphone</span>
                </button>

                <button type="button" class="choice-option" id="nameChoice" onclick="switchSearchMode('name')">
                    <span>👤</span>
                    <span data-translate="choice.identity">Par identité</span>
                </button>
            </div>
        </div>
        
        <p id="mainDescription" data-translate="main.description">Veuillez saisir votre numéro précedé de l'indicatif et cliquez sur "Vérifiez"</p>

        <form class="form" id="vaccinForm">
            <!-- Section téléphone -->
            <div class="form-section active" id="phoneSection">
                <div class="input-group">
                    <label for="phone" data-translate="form.phone.label">Numéro de téléphone</label>
                    <input type="tel" id="phone" data-translate-placeholder="form.phone.placeholder" placeholder="Ex: 2250769989178">
                </div>
            </div>

            <!-- Section identité -->
            <div class="form-section" id="nameSection">
                <div class="input-group">
                    <label for="lastName" data-translate="form.lastName.label">Nom de famille</label>
                    <input type="text" id="lastName" data-translate-placeholder="form.lastName.placeholder" placeholder="Ex: KOUAME">
                </div>
                <div class="input-group">
                    <label for="firstName" data-translate="form.firstName.label">Prénom</label>
                    <input type="text" id="firstName" data-translate-placeholder="form.firstName.placeholder" placeholder="Ex: Jean">
                </div>
                <div class="input-group">
                    <label for="birthDate" data-translate="form.birthDate.label">Date de naissance</label>
                    <input type="date" id="birthDate" max="2010-12-31" min="1900-01-01" data-translate-placeholder="form.birthDate.placeholder" placeholder="jj/mm/aaaa">
                </div>
            </div>

            <button type="submit" class="btn" id="submitBtn" data-translate="form.submit">Vérifiez</button>
        </form>
        
        <div id="message"></div>
    </div>

    <div id="results"></div>

    <script>
        // Variables globales
        let currentSearchMode = 'phone';
        let currentLanguage = 'fr';

        // Traductions
        const translations = {
            fr: {
                'header.title': 'CARNET DE VACCINATION ÉLECTRONIQUE',
                'header.country': 'République de Côte d\'Ivoire',
                'main.title': 'Vérification de la vaccination contre la fièvre jaune',
                'choice.title': 'Choisissez votre méthode de recherche',
                'choice.or': 'OU',
                'choice.phone': 'Par téléphone',
                'choice.identity': 'Par identité',
                'main.description': 'Veuillez saisir votre numéro précedé de l\'indicatif et cliquez sur "Vérifiez"',
                'main.description.identity': 'Veuillez saisir votre nom, prénom et date de naissance puis cliquez sur "Vérifiez"',
                'form.phone.label': 'Numéro de téléphone',
                'form.phone.placeholder': 'Ex: 2250769989178',
                'form.lastName.label': 'Nom de famille',
                'form.lastName.placeholder': 'Ex: KOUAME',
                'form.firstName.label': 'Prénom',
                'form.firstName.placeholder': 'Ex: Jean',
                'form.birthDate.label': 'Date de naissance',
                'form.birthDate.placeholder': 'jj/mm/aaaa',
                'form.submit': 'Vérifiez',
                'error.phone.required': 'Veuillez saisir un numéro de téléphone',
                'error.phone.format': 'Format invalide. Utilisez le format: 2250769989178 (sans le +)',
                'error.identity.required': 'Veuillez remplir tous les champs (nom, prénom et date de naissance)',
                'error.not.found': 'Carnet non trouvé',
                'error.connection': 'Erreur de connexion au serveur. Veuillez réessayer.',
                'loading': 'Vérification...'
            },
            en: {
                'header.title': 'ELECTRONIC VACCINATION CARD',
                'header.country': 'Republic of Côte d\'Ivoire',
                'main.title': 'Yellow fever vaccination verification',
                'choice.title': 'Choose your search method',
                'choice.or': 'OR',
                'choice.phone': 'By phone',
                'choice.identity': 'By identity',
                'main.description': 'Please enter your number with country code and click "Verify"',
                'main.description.identity': 'Please enter your last name, first name and date of birth then click "Verify"',
                'form.phone.label': 'Phone number',
                'form.phone.placeholder': 'Ex: 2250769989178',
                'form.lastName.label': 'Last name',
                'form.lastName.placeholder': 'Ex: KOUAME',
                'form.firstName.label': 'First name',
                'form.firstName.placeholder': 'Ex: Jean',
                'form.birthDate.label': 'Date of birth',
                'form.birthDate.placeholder': 'dd/mm/yyyy',
                'form.submit': 'Verify',
                'error.phone.required': 'Please enter a phone number',
                'error.phone.format': 'Invalid format. Use format: 2250769989178 (without +)',
                'error.identity.required': 'Please fill in all fields (last name, first name and date of birth)',
                'error.not.found': 'Card not found',
                'error.connection': 'Server connection error. Please try again.',
                'loading': 'Verifying...'
            },
            ar: {
                'header.title': 'بطاقة التطعيم الإلكترونية',
                'header.country': 'جمهورية كوت ديفوار',
                'main.title': 'التحقق من تطعيم الحمى الصفراء',
                'choice.title': 'اختر طريقة البحث',
                'choice.or': 'أو',
                'choice.phone': 'بالهاتف',
                'choice.identity': 'بالهوية',
                'main.description': 'يرجى إدخال رقمك مع رمز البلد والنقر على "تحقق"',
                'main.description.identity': 'يرجى إدخال اسم العائلة والاسم الأول وتاريخ الميلاد ثم النقر على "تحقق"',
                'form.phone.label': 'رقم الهاتف',
                'form.phone.placeholder': 'مثال: 2250769989178',
                'form.lastName.label': 'اسم العائلة',
                'form.lastName.placeholder': 'مثال: كوامي',
                'form.firstName.label': 'الاسم الأول',
                'form.firstName.placeholder': 'مثال: جان',
                'form.birthDate.label': 'تاريخ الميلاد',
                'form.birthDate.placeholder': 'يوم/شهر/سنة',
                'form.submit': 'تحقق',
                'error.phone.required': 'يرجى إدخال رقم الهاتف',
                'error.phone.format': 'تنسيق غير صحيح. استخدم التنسيق: 2250769989178 (بدون +)',
                'error.identity.required': 'يرجى ملء جميع الحقول (اسم العائلة والاسم الأول وتاريخ الميلاد)',
                'error.not.found': 'البطاقة غير موجودة',
                'error.connection': 'خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
                'loading': 'جاري التحقق...'
            }
        };

        // Fonctions de gestion des langues
        function toggleLanguageMenu() {
            const dropdown = document.getElementById('languageDropdown');
            dropdown.classList.toggle('open');
        }

        function changeLanguage(lang) {
            currentLanguage = lang;

            // Mettre à jour les options actives
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeLanguage('${lang}')"]`).classList.add('active');

            // Appliquer les traductions
            applyTranslations(lang);

            // Gérer RTL pour l'arabe
            if (lang === 'ar') {
                document.body.classList.add('rtl');
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.body.classList.remove('rtl');
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', lang);
            }

            // Fermer le menu
            document.getElementById('languageDropdown').classList.remove('open');

            // Sauvegarder la préférence
            localStorage.setItem('selectedLanguage', lang);
        }

        function applyTranslations(lang) {
            const texts = translations[lang];

            // Traduire tous les éléments avec data-translate
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (texts[key]) {
                    element.textContent = texts[key];
                }
            });

            // Traduire les placeholders
            document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
                const key = element.getAttribute('data-translate-placeholder');
                if (texts[key]) {
                    element.placeholder = texts[key];
                }
            });

            // Mettre à jour la description selon le mode actuel
            updateDescription();
        }

        function updateDescription() {
            const mainDescription = document.getElementById('mainDescription');
            const key = currentSearchMode === 'phone' ? 'main.description' : 'main.description.identity';
            const text = translations[currentLanguage][key];
            if (text) {
                mainDescription.textContent = text;
            }
        }

        function getTranslatedText(key) {
            return translations[currentLanguage][key] || key;
        }

        // Fermer le menu de langue en cliquant ailleurs
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageDropdown');
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });

        // Fonction pour basculer entre les modes
        function switchSearchMode(mode) {
            try {
                currentSearchMode = mode;

                const phoneChoice = document.getElementById('phoneChoice');
                const nameChoice = document.getElementById('nameChoice');
                const phoneSection = document.getElementById('phoneSection');
                const nameSection = document.getElementById('nameSection');
                const mainDescription = document.getElementById('mainDescription');
                const choiceIndicator = document.getElementById('choiceIndicator');

                if (mode === 'phone') {
                    phoneChoice.classList.add('selected');
                    nameChoice.classList.remove('selected');
                    phoneSection.classList.add('active');
                    nameSection.classList.remove('active');
                    choiceIndicator.classList.remove('name-mode');
                } else {
                    phoneChoice.classList.remove('selected');
                    nameChoice.classList.add('selected');
                    phoneSection.classList.remove('active');
                    nameSection.classList.add('active');
                    choiceIndicator.classList.add('name-mode');
                }

                // Mettre à jour la description selon la langue actuelle
                updateDescription();

                clearMessage();
            } catch (error) {
                console.error('Erreur lors du changement de mode:', error);
            }
        }

        // Gestion du formulaire
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Charger la langue sauvegardée
                const savedLanguage = localStorage.getItem('selectedLanguage') || 'fr';
                if (savedLanguage !== 'fr') {
                    changeLanguage(savedLanguage);
                }

                const form = document.getElementById('vaccinForm');
                if (form) {
                    form.addEventListener('submit', handleFormSubmit);
                }
            } catch (error) {
                console.error('Erreur lors de l\'initialisation:', error);
            }
        });

        async function handleFormSubmit(e) {
            e.preventDefault();

            try {
                if (currentSearchMode === 'phone') {
                    await handlePhoneSearch();
                } else {
                    await handleNameSearch();
                }
            } catch (error) {
                console.error('Erreur lors de la soumission:', error);
                showMessage('Une erreur est survenue. Veuillez réessayer.', 'error');
            }
        }

        async function handlePhoneSearch() {
            const phoneInput = document.getElementById('phone');
            const phoneNumber = phoneInput ? phoneInput.value.trim() : '';

            if (!phoneNumber) {
                showMessage('Veuillez saisir un numéro de téléphone', 'error');
                return;
            }

            if (!/^[1-9]\d{9,14}$/.test(phoneNumber)) {
                showMessage('Format invalide. Utilisez le format: 2250769989178 (sans le +)', 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tel: phoneNumber, d: 'PROD' })
                });

                const data = await response.json();

                if (data.code === 0 && data.data && data.data.length > 0) {
                    showMessage('Carnet trouvé ! Redirection...', 'loading');
                    // Ici vous pouvez ajouter la logique d'affichage des résultats
                } else {
                    showMessage(data.msg || 'Carnet non trouvé', 'error');
                }
            } catch (error) {
                console.error('Erreur API:', error);
                showMessage('Erreur de connexion au serveur. Veuillez réessayer.', 'error');
            } finally {
                setLoading(false);
            }
        }

        async function handleNameSearch() {
            const lastNameInput = document.getElementById('lastName');
            const firstNameInput = document.getElementById('firstName');
            const birthDateInput = document.getElementById('birthDate');

            const lastName = lastNameInput ? lastNameInput.value.trim().toUpperCase() : '';
            const firstName = firstNameInput ? firstNameInput.value.trim().toUpperCase() : '';
            const birthDate = birthDateInput ? birthDateInput.value : '';

            if (!lastName || !firstName || !birthDate) {
                showMessage('Veuillez remplir tous les champs (nom, prénom et date de naissance)', 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcodeplus', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        d: 'PROD',
                        nompat: lastName,
                        prenompat: firstName,
                        datenaissancepat: birthDate
                    })
                });

                const data = await response.json();

                if (data.code === 0 && data.data && data.data.length > 0) {
                    showMessage('Carnet trouvé ! Redirection...', 'loading');
                    // Ici vous pouvez ajouter la logique d'affichage des résultats
                } else {
                    showMessage(data.msg || 'Aucun carnet trouvé avec ces informations', 'error');
                }
            } catch (error) {
                console.error('Erreur API:', error);
                showMessage('Erreur de connexion au serveur. Veuillez réessayer.', 'error');
            } finally {
                setLoading(false);
            }
        }

        function setLoading(loading) {
            try {
                const submitBtn = document.getElementById('submitBtn');
                if (submitBtn) {
                    submitBtn.disabled = loading;
                    submitBtn.textContent = loading ? 'Vérification...' : 'Vérifiez';
                }
                if (loading) {
                    showMessage('Recherche en cours...', 'loading');
                }
            } catch (error) {
                console.error('Erreur setLoading:', error);
            }
        }

        function showMessage(text, type) {
            try {
                const messageDiv = document.getElementById('message');
                if (messageDiv) {
                    messageDiv.innerHTML = `<div class="${type}">${text}</div>`;
                }
            } catch (error) {
                console.error('Erreur showMessage:', error);
            }
        }

        function clearMessage() {
            try {
                const messageDiv = document.getElementById('message');
                if (messageDiv) {
                    messageDiv.innerHTML = '';
                }
            } catch (error) {
                console.error('Erreur clearMessage:', error);
            }
        }
    </script>
</body>
</html>
