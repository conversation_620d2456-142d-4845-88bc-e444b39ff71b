# Configuration Apache pour Carnet de Vaccination - Production
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirection vers index.html pour SPA
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . index.html [L]
</IfModule>

# Gestion du cache pour les favicons (forcer le rechargement)
<IfModule mod_headers.c>
    <FilesMatch "\.(ico|png)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# Cache optimise pour la production (autres fichiers)
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE image/svg+xml
</IfModule>

# Securite renforcee
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Desactiver l'affichage des repertoires
Options -Indexes

# Protection des fichiers sensibles
<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# Types MIME pour favicons
AddType image/x-icon .ico
AddType image/png .png
