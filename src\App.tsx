import { useState } from 'react';
import PhoneInput from './components/PhoneInput';
import VaccinationList from './components/VaccinationList';
import { vaccinationApi } from './services/api';
import type { VaccinationRecord } from './services/api';
import './App.css';

function App() {
  const [loading, setLoading] = useState(false);
  const [vaccinationRecords, setVaccinationRecords] = useState<VaccinationRecord[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState('');

  const handleVerifyPhone = async (phoneNumber: string) => {
    setLoading(true);
    setError('');

    try {
      console.log('🔍 Appel API avec le numéro:', phoneNumber);
      const response = await vaccinationApi.getVaccinationRecords(phoneNumber);
      console.log('📡 Réponse API:', response);

      if (response.code === 0 && response.data) {
        console.log('✅ Données trouvées:', response.data.length, 'vaccination(s)');
        setVaccinationRecords(response.data);
        setShowResults(true);
      } else {
        console.log('❌ Aucune donnée trouvée:', response.msg);
        setError(response.msg || 'Carnet non trouvé');
        setVaccinationRecords([]);
      }
    } catch (err) {
      console.error('🚨 Erreur API:', err);
      setError('Erreur de connexion au serveur. Veuillez réessayer.');
      setVaccinationRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToSearch = () => {
    setShowResults(false);
    setVaccinationRecords([]);
    setError('');
  };

  return (
    <div className="app">
      {!showResults ? (
        <div>
          <PhoneInput onVerify={handleVerifyPhone} loading={loading} />
          {error && (
            <div className="error-container">
              <p className="error-message">{error}</p>
            </div>
          )}
        </div>
      ) : (
        <VaccinationList
          records={vaccinationRecords}
          onBack={handleBackToSearch}
        />
      )}
    </div>
  );
}

export default App;
