<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic - Erreur 500</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error { color: #d32f2f; background: #ffebee; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .success { color: #2e7d32; background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #f57c00; background: #fff3e0; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #1976d2; background: #e3f2fd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1565c0; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Diagnostic d'Erreur 500</h1>
        
        <div class="info">
            <strong>Objectif :</strong> Identifier la cause de l'erreur 500 Internal Server Error
        </div>
        
        <div class="test-section">
            <h3>1. Test de Base</h3>
            <p>Si vous voyez cette page, le serveur peut servir des fichiers HTML statiques.</p>
            <div class="success">✅ Serveur web fonctionnel</div>
        </div>
        
        <div class="test-section">
            <h3>2. Test JavaScript</h3>
            <button onclick="testJavaScript()">Tester JavaScript</button>
            <div id="jsResult"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Test des Ressources</h3>
            <button onclick="testResources()">Tester les Images</button>
            <div id="resourceResult"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Test API</h3>
            <button onclick="testAPI()">Tester l'API</button>
            <div id="apiResult"></div>
        </div>
        
        <div class="test-section">
            <h3>5. Informations Système</h3>
            <button onclick="showSystemInfo()">Afficher les Infos</button>
            <div id="systemInfo"></div>
        </div>
        
        <div class="test-section">
            <h3>6. Solutions Possibles</h3>
            <div class="warning">
                <strong>Causes communes d'erreur 500 :</strong>
                <ul>
                    <li>Permissions de fichiers incorrectes (chmod 644 pour les fichiers, 755 pour les dossiers)</li>
                    <li>Fichier .htaccess corrompu ou mal configuré</li>
                    <li>Erreur de syntaxe dans le code (HTML/CSS/JS)</li>
                    <li>Ressources manquantes (images, CSS, JS)</li>
                    <li>Limite de mémoire ou de temps d'exécution dépassée</li>
                    <li>Configuration du serveur web incorrecte</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>7. Actions Recommandées</h3>
            <ol>
                <li>Vérifiez les logs du serveur web (Apache/Nginx)</li>
                <li>Testez avec le fichier <code>test-simple.html</code></li>
                <li>Utilisez <code>index-backup.html</code> comme version simplifiée</li>
                <li>Vérifiez les permissions des fichiers</li>
                <li>Supprimez temporairement le fichier .htaccess s'il existe</li>
            </ol>
        </div>
    </div>
    
    <script>
        function testJavaScript() {
            const result = document.getElementById('jsResult');
            try {
                const test = {
                    date: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };
                result.innerHTML = '<div class="success">✅ JavaScript fonctionne correctement</div><pre>' + JSON.stringify(test, null, 2) + '</pre>';
            } catch (error) {
                result.innerHTML = '<div class="error">❌ Erreur JavaScript: ' + error.message + '</div>';
            }
        }
        
        function testResources() {
            const result = document.getElementById('resourceResult');
            const resources = ['logo_sante.jpg', 'logo_inhp.jpg', 'vite.svg'];
            let html = '';
            
            resources.forEach(resource => {
                const img = new Image();
                img.onload = function() {
                    html += '<div class="success">✅ ' + resource + ' - OK</div>';
                    result.innerHTML = html;
                };
                img.onerror = function() {
                    html += '<div class="error">❌ ' + resource + ' - Introuvable</div>';
                    result.innerHTML = html;
                };
                img.src = resource;
            });
        }
        
        async function testAPI() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '<div class="info">🔄 Test en cours...</div>';
            
            try {
                const response = await fetch('https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ tel: '2250000000000', d: 'PROD' })
                });
                
                const data = await response.json();
                result.innerHTML = '<div class="success">✅ API accessible</div><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                result.innerHTML = '<div class="warning">⚠️ API non accessible (normal si pas de connexion): ' + error.message + '</div>';
            }
        }
        
        function showSystemInfo() {
            const result = document.getElementById('systemInfo');
            const info = {
                timestamp: new Date().toLocaleString('fr-FR'),
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                url: window.location.href,
                referrer: document.referrer,
                title: document.title
            };
            
            result.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
        }
        
        // Auto-test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Page de diagnostic chargée');
            testJavaScript();
        });
    </script>
</body>
</html>
