<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="page.title">Bienvenue - Carnet de Vaccination</title>
    
    <!-- Favicons officiels avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    <link rel="manifest" href="site.webmanifest?v=2025">
    
    <!-- Meta tags pour mobile -->
    <meta name="theme-color" content="#4caf50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Carnet Vaccin">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }
        
        /* Styles pour le sélecteur de langue */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .language-dropdown {
            position: relative;
            display: inline-block;
        }
        
        .language-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            padding: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(76, 175, 80, 0.2);
        }
        
        .language-button:hover {
            background: #4caf50;
            color: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .language-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #4caf50;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            min-width: 160px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            margin-top: 8px;
        }
        
        .language-dropdown.open .language-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .language-option:last-child {
            border-bottom: none;
        }
        
        .language-option:hover {
            background: #f1f8e9;
            color: #2e7d32;
        }
        
        .language-option.active {
            background: #4caf50;
            color: white;
        }
        
        .welcome-card {
            background: white;
            border-radius: 16px;
            padding: 40px;
            margin-top: 60px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            object-fit: contain;
        }
        
        h1 {
            color: #1b5e20;
            font-size: 28px;
            margin-bottom: 15px;
        }
        
        .subtitle {
            color: #2e7d32;
            font-size: 18px;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .description {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .feature h3 {
            color: #1b5e20;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .feature p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        /* Support RTL pour l'arabe */
        .rtl {
            direction: rtl;
            text-align: right;
        }
        
        .rtl .language-selector {
            left: 20px;
            right: auto;
        }
        
        .rtl .language-menu {
            left: 0;
            right: auto;
        }
        
        .rtl .welcome-card {
            text-align: right;
        }
        
        .rtl .feature {
            text-align: right;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .language-selector {
                top: 15px;
                right: 15px;
            }
            
            .language-button {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            
            .welcome-card {
                padding: 30px 20px;
                margin-top: 50px;
            }
            
            .logo-section {
                flex-direction: column;
                gap: 15px;
            }
            
            .logo {
                width: 60px;
                height: 60px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 16px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sélecteur de langue -->
        <div class="language-selector">
            <div class="language-dropdown" id="languageDropdown">
                <button class="language-button" onclick="toggleLanguageMenu()" title="Changer de langue / Change language / تغيير اللغة">
                    <span class="language-icon">🌐</span>
                </button>
                <div class="language-menu" id="languageMenu">
                    <div class="language-option active" onclick="changeLanguage('fr')">
                        <span class="flag">🇫🇷</span>
                        <span>Français</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('en')">
                        <span class="flag">🇬🇧</span>
                        <span>English</span>
                    </div>
                    <div class="language-option" onclick="changeLanguage('ar')">
                        <span class="flag">🇸🇦</span>
                        <span>العربية</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <div class="logo-section">
                <img src="logo_sante.jpg" alt="Ministère de la Santé" class="logo">
                <img src="logo_inhp.jpg" alt="INHP" class="logo">
            </div>
            
            <h1 data-translate="welcome.title">Bienvenue</h1>
            <div class="subtitle" data-translate="welcome.subtitle">Carnet de Vaccination Électronique</div>
            <div class="description" data-translate="welcome.description">
                Vérifiez facilement votre statut de vaccination contre la fièvre jaune. 
                Notre système sécurisé vous permet de consulter votre carnet de vaccination 
                électronique en quelques clics.
            </div>
            
            <a href="index.html" class="cta-button" data-translate="welcome.start">
                Commencer la vérification
            </a>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🔍</div>
                    <h3 data-translate="feature.search.title">Recherche Facile</h3>
                    <p data-translate="feature.search.desc">Par téléphone ou par identité</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🛡️</div>
                    <h3 data-translate="feature.secure.title">Sécurisé</h3>
                    <p data-translate="feature.secure.desc">Données protégées et cryptées</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3 data-translate="feature.fast.title">Rapide</h3>
                    <p data-translate="feature.fast.desc">Résultats instantanés</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentLanguage = 'fr';

        // Traductions
        const translations = {
            fr: {
                'page.title': 'Bienvenue - Carnet de Vaccination',
                'welcome.title': 'Bienvenue',
                'welcome.subtitle': 'Carnet de Vaccination Électronique',
                'welcome.description': 'Vérifiez facilement votre statut de vaccination contre la fièvre jaune. Notre système sécurisé vous permet de consulter votre carnet de vaccination électronique en quelques clics.',
                'welcome.start': 'Commencer la vérification',
                'feature.search.title': 'Recherche Facile',
                'feature.search.desc': 'Par téléphone ou par identité',
                'feature.secure.title': 'Sécurisé',
                'feature.secure.desc': 'Données protégées et cryptées',
                'feature.fast.title': 'Rapide',
                'feature.fast.desc': 'Résultats instantanés'
            },
            en: {
                'page.title': 'Welcome - Vaccination Card',
                'welcome.title': 'Welcome',
                'welcome.subtitle': 'Electronic Vaccination Card',
                'welcome.description': 'Easily verify your yellow fever vaccination status. Our secure system allows you to check your electronic vaccination card in just a few clicks.',
                'welcome.start': 'Start Verification',
                'feature.search.title': 'Easy Search',
                'feature.search.desc': 'By phone or by identity',
                'feature.secure.title': 'Secure',
                'feature.secure.desc': 'Protected and encrypted data',
                'feature.fast.title': 'Fast',
                'feature.fast.desc': 'Instant results'
            },
            ar: {
                'page.title': 'مرحباً - بطاقة التطعيم',
                'welcome.title': 'مرحباً',
                'welcome.subtitle': 'بطاقة التطعيم الإلكترونية',
                'welcome.description': 'تحقق بسهولة من حالة تطعيمك ضد الحمى الصفراء. يتيح لك نظامنا الآمن فحص بطاقة التطعيم الإلكترونية الخاصة بك في بضع نقرات.',
                'welcome.start': 'بدء التحقق',
                'feature.search.title': 'بحث سهل',
                'feature.search.desc': 'بالهاتف أو بالهوية',
                'feature.secure.title': 'آمن',
                'feature.secure.desc': 'بيانات محمية ومشفرة',
                'feature.fast.title': 'سريع',
                'feature.fast.desc': 'نتائج فورية'
            }
        };

        // Fonctions de gestion des langues
        function toggleLanguageMenu() {
            const dropdown = document.getElementById('languageDropdown');
            dropdown.classList.toggle('open');
        }

        function changeLanguage(lang) {
            currentLanguage = lang;
            
            // Mettre à jour les options actives
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
            });
            document.querySelector(`[onclick="changeLanguage('${lang}')"]`).classList.add('active');
            
            // Appliquer les traductions
            applyTranslations(lang);
            
            // Gérer RTL pour l'arabe
            if (lang === 'ar') {
                document.body.classList.add('rtl');
                document.documentElement.setAttribute('dir', 'rtl');
                document.documentElement.setAttribute('lang', 'ar');
            } else {
                document.body.classList.remove('rtl');
                document.documentElement.setAttribute('dir', 'ltr');
                document.documentElement.setAttribute('lang', lang);
            }
            
            // Fermer le menu
            document.getElementById('languageDropdown').classList.remove('open');
            
            // Sauvegarder la préférence
            localStorage.setItem('selectedLanguage', lang);
        }

        function applyTranslations(lang) {
            const texts = translations[lang];
            
            // Traduire tous les éléments avec data-translate
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (texts[key]) {
                    if (element.tagName === 'TITLE') {
                        element.textContent = texts[key];
                    } else {
                        element.textContent = texts[key];
                    }
                }
            });
        }

        // Fermer le menu de langue en cliquant ailleurs
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('languageDropdown');
            if (!dropdown.contains(event.target)) {
                dropdown.classList.remove('open');
            }
        });

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Charger la langue sauvegardée
            const savedLanguage = localStorage.getItem('selectedLanguage') || 'fr';
            if (savedLanguage !== 'fr') {
                changeLanguage(savedLanguage);
            }
        });
    </script>
</body>
</html>
