<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démo Responsive - Sélecteur de Langue 🌐</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .demo-language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .demo-button {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid white;
            border-radius: 50%;
            padding: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #2e7d32;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        .demo-button:hover {
            background: white;
            transform: translateY(-2px) scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .content {
            padding: 30px;
        }
        .size-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .device-demo {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e0e0e0;
            position: relative;
        }
        .device-demo h3 {
            color: #1b5e20;
            margin-bottom: 15px;
        }
        .device-frame {
            background: #333;
            border-radius: 12px;
            padding: 15px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }
        .device-screen {
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            border-radius: 8px;
            padding: 10px;
            position: relative;
            min-height: 120px;
        }
        .mock-selector {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #4caf50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #2e7d32;
            transition: all 0.3s ease;
        }
        .desktop .mock-selector {
            width: 44px;
            height: 44px;
            font-size: 20px;
        }
        .tablet .mock-selector {
            width: 40px;
            height: 40px;
            font-size: 18px;
        }
        .mobile .mock-selector {
            width: 36px;
            height: 36px;
            font-size: 16px;
        }
        .mock-content {
            background: white;
            border-radius: 6px;
            padding: 8px;
            margin-top: 25px;
            font-size: 10px;
            color: #666;
        }
        .features {
            background: #f1f8e9;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        .features h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
        }
        .feature-item h4 {
            color: #1b5e20;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .feature-item p {
            font-size: 12px;
            color: #666;
            margin: 0;
        }
        .comparison {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        .comparison h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        .comparison-table {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        .before, .after {
            background: white;
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            border-left: 4px solid #ff9800;
        }
        .after {
            border-left: 4px solid #4caf50;
        }
        .before h4 {
            color: #f57c00;
        }
        .after h4 {
            color: #2e7d32;
        }
        @media (max-width: 768px) {
            .demo-language-selector {
                top: 15px;
                right: 15px;
            }
            .demo-button {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }
            .size-demo {
                grid-template-columns: 1fr;
            }
            .comparison-table {
                grid-template-columns: 1fr;
            }
        }
        @media (max-width: 480px) {
            .demo-language-selector {
                top: 12px;
                right: 12px;
            }
            .demo-button {
                width: 36px;
                height: 36px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="demo-language-selector">
                <button class="demo-button" title="Sélecteur responsive 🌐">
                    🌐
                </button>
            </div>
            <h1>🌐 Démo Responsive - Sélecteur de Langue</h1>
            <p>Design adaptatif avec icône universelle</p>
        </div>
        
        <div class="content">
            <div class="features">
                <h3>✨ Nouveau Design avec Icône 🌐</h3>
                <p>Le sélecteur de langue utilise maintenant l'icône universelle 🌐 pour une meilleure visibilité en mode responsive.</p>
                
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>🎯 Icône Universelle</h4>
                        <p>L'icône 🌐 est reconnue internationalement comme symbole de langues/web</p>
                    </div>
                    <div class="feature-item">
                        <h4>📱 Design Circulaire</h4>
                        <p>Bouton rond compact qui s'adapte parfaitement aux écrans tactiles</p>
                    </div>
                    <div class="feature-item">
                        <h4>📏 Tailles Adaptatives</h4>
                        <p>44px → 40px → 36px selon la taille d'écran</p>
                    </div>
                    <div class="feature-item">
                        <h4>✨ Animations Fluides</h4>
                        <p>Effet hover avec élévation et scale (1.1x) pour le feedback visuel</p>
                    </div>
                </div>
            </div>
            
            <div class="size-demo">
                <div class="device-demo desktop">
                    <h3>🖥️ Desktop (44px)</h3>
                    <div class="device-frame">
                        <div class="device-screen">
                            <div class="mock-selector">🌐</div>
                            <div class="mock-content">
                                Interface complète<br>
                                Taille optimale pour souris<br>
                                Position coin supérieur droit
                            </div>
                        </div>
                    </div>
                    <p><strong>Taille :</strong> 44x44px | <strong>Police :</strong> 20px</p>
                </div>
                
                <div class="device-demo tablet">
                    <h3>📱 Tablette (40px)</h3>
                    <div class="device-frame">
                        <div class="device-screen">
                            <div class="mock-selector">🌐</div>
                            <div class="mock-content">
                                Interface adaptée<br>
                                Taille pour tactile<br>
                                Espacement optimisé
                            </div>
                        </div>
                    </div>
                    <p><strong>Taille :</strong> 40x40px | <strong>Police :</strong> 18px</p>
                </div>
                
                <div class="device-demo mobile">
                    <h3>📱 Mobile (36px)</h3>
                    <div class="device-frame">
                        <div class="device-screen">
                            <div class="mock-selector">🌐</div>
                            <div class="mock-content">
                                Interface compacte<br>
                                Optimisé pour pouce<br>
                                Menu adaptatif
                            </div>
                        </div>
                    </div>
                    <p><strong>Taille :</strong> 36x36px | <strong>Police :</strong> 16px</p>
                </div>
            </div>
            
            <div class="comparison">
                <h3>🔄 Avant vs Après</h3>
                <div class="comparison-table">
                    <div class="before">
                        <h4>❌ Ancien Design</h4>
                        <ul>
                            <li>Bouton rectangulaire avec drapeau + texte</li>
                            <li>Trop large sur mobile</li>
                            <li>Texte illisible sur petits écrans</li>
                            <li>Pas assez compact</li>
                        </ul>
                    </div>
                    <div class="after">
                        <h4>✅ Nouveau Design</h4>
                        <ul>
                            <li>Bouton circulaire avec icône 🌐 seule</li>
                            <li>Parfaitement adapté au mobile</li>
                            <li>Icône universellement reconnue</li>
                            <li>Design compact et élégant</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background: #fff3e0; padding: 20px; border-radius: 12px; margin: 30px 0;">
                <h3 style="color: #f57c00; margin-bottom: 15px;">🧪 Test en Temps Réel</h3>
                <p>Redimensionnez cette fenêtre pour voir l'icône 🌐 en haut à droite s'adapter automatiquement !</p>
                <ul>
                    <li><strong>Large (>768px) :</strong> Icône 44px</li>
                    <li><strong>Moyen (≤768px) :</strong> Icône 40px</li>
                    <li><strong>Petit (≤480px) :</strong> Icône 36px</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" style="background: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px;">
                    🏠 Tester l'Application
                </a>
                <a href="test-languages.html" style="background: #2196f3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 5px;">
                    🌐 Test des Langues
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Afficher la taille actuelle de l'icône
        function updateSizeInfo() {
            const width = window.innerWidth;
            let size, device;
            
            if (width <= 480) {
                size = "36px";
                device = "Mobile";
            } else if (width <= 768) {
                size = "40px";
                device = "Tablette";
            } else {
                size = "44px";
                device = "Desktop";
            }
            
            console.log(`🌐 Taille actuelle: ${size} (${device}) - Largeur: ${width}px`);
        }
        
        // Mettre à jour au redimensionnement
        window.addEventListener('resize', updateSizeInfo);
        document.addEventListener('DOMContentLoaded', updateSizeInfo);
        
        // Animation de démonstration
        document.querySelector('.demo-button').addEventListener('click', function() {
            this.style.transform = 'translateY(-2px) scale(1.2)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
            
            alert('🌐 Sélecteur de langue responsive !\n\n' +
                  '✅ Design circulaire compact\n' +
                  '✅ Icône universelle 🌐\n' +
                  '✅ Adaptation automatique\n' +
                  '✅ Optimisé pour mobile');
        });
    </script>
</body>
</html>
