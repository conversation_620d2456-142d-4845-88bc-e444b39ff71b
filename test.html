<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Carnet de Vaccination Final</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%);
            min-height: 100vh; margin: 0; display: flex; align-items: center; justify-content: center;
        }
        .container {
            max-width: 600px; background: white; padding: 40px; border-radius: 15px;
            box-shadow: 0 8px 25px rgba(27, 94, 32, 0.15); text-align: center; border: 1px solid #c8e6c9;
        }
        .header { display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 20px; }
        .logo { width: 60px; height: 60px; border-radius: 8px; object-fit: contain; }
        .success { color: #1b5e20; font-weight: bold; font-size: 24px; margin-bottom: 15px; }
        .info { color: #e65100; margin-bottom: 15px; }
        .link { 
            display: inline-block; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; text-decoration: none; padding: 15px 30px; border-radius: 10px;
            font-weight: 600; margin-top: 20px; transition: transform 0.2s;
        }
        .link:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="logo_sante.jpg" alt="Ministere Sante" class="logo">
            <img src="logo_inhp.jpg" alt="INHP" class="logo">
        </div>
        <div class="success">Test de deploiement final reussi !</div>
        <p class="info">Application Carnet de Vaccination Electronique</p>
        <p class="info">Version: Drapeau Ivoirien v2.0 Final</p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
        <p><strong>Assets:</strong> Logos charges avec succes</p>
        <a href="index.html" class="link">Acceder a l'application</a>
    </div>
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('fr-FR');
    </script>
</body>
</html>
