import React, { useState } from 'react';
import type { VaccinationRecord } from '../services/api';
import VaccinationCard from './VaccinationCard';

interface VaccinationListProps {
  records: VaccinationRecord[];
  onBack: () => void;
}

const VaccinationList: React.FC<VaccinationListProps> = ({ records, onBack }) => {
  const [selectedRecord, setSelectedRecord] = useState<VaccinationRecord | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  };

  const handleViewDetails = (record: VaccinationRecord) => {
    setSelectedRecord(record);
  };

  const handleCloseCard = () => {
    setSelectedRecord(null);
  };

  if (records.length === 0) {
    return (
      <div className="vaccination-list-container">
        <div className="no-records">
          <div style={{ fontSize: '64px', marginBottom: '20px', color: '#546e7a' }}>
            🔍📋
          </div>
          <h3>Aucun carnet de vaccination trouvé</h3>
          <p>Aucune donnée de vaccination n'a été trouvée pour ce numéro de téléphone.</p>
          <button className="back-button" onClick={onBack}>
            Nouvelle recherche
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="vaccination-list-container">
      <div className="list-header">
        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
          <span style={{ fontSize: '32px', color: '#1976d2' }}>📋💉</span>
          <div>
            <h2>Carnet de Vaccination Électronique</h2>
            <p>{records.length} vaccination(s) trouvée(s)</p>
          </div>
        </div>
        <button className="back-button" onClick={onBack}>
          Nouvelle recherche
        </button>
      </div>

      <div className="vaccination-table">
        <table>
          <thead>
            <tr>
              <th>Patient</th>
              <th>Type de Vaccin</th>
              <th>Date de Vaccination</th>
              <th>Centre</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {records.map((record, index) => (
              <tr key={`${record.IDCAL}-${index}`}>
                <td>
                  <div className="patient-cell">
                    <div className="patient-name">
                      {record.NOMPAT} {record.PRENOMPAT}
                    </div>
                    <div className="patient-phone">+{record.NUMEROPAT}</div>
                  </div>
                </td>
                <td>
                  <span className="vaccine-badge">{record.NOMVAC}</span>
                </td>
                <td>{formatDate(record.PRESENCE)}</td>
                <td>{record.NOMCENTR}</td>
                <td>
                  <button
                    className="view-button"
                    onClick={() => handleViewDetails(record)}
                    title="Voir les détails de la vaccination"
                  >
                    <span className="button-icon">👁️</span>
                    <span className="button-text">Détails</span>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedRecord && (
        <VaccinationCard 
          record={selectedRecord} 
          onClose={handleCloseCard}
        />
      )}
    </div>
  );
};

export default VaccinationList;
