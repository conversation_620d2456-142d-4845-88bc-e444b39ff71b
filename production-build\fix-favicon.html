<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Favicon - Test et Solutions</title>
    
    <!-- Favicons avec cache busting -->
    <link rel="icon" type="image/x-icon" href="favicon.ico?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png?v=2025">
    <link rel="manifest" href="site.webmanifest?v=2025">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1b5e20;
            text-align: center;
            margin-bottom: 30px;
        }
        .solution {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4caf50;
        }
        .solution h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }
        .code {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: 600;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
        }
        button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .favicon-preview {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Favicon - Solutions pour Production</h1>
        
        <div class="test-section">
            <h3>🧪 Test de Chargement des Favicons</h3>
            <p>Vérification de l'accessibilité des fichiers favicon :</p>
            
            <div id="faviconTests">
                <div>
                    <strong>favicon.ico :</strong>
                    <img src="favicon.ico" class="favicon-preview" id="ico-test" alt="ICO">
                    <span id="ico-status">Test...</span>
                </div>
                <div>
                    <strong>favicon-32x32.png :</strong>
                    <img src="favicon-32x32.png" class="favicon-preview" id="png32-test" alt="PNG32">
                    <span id="png32-status">Test...</span>
                </div>
                <div>
                    <strong>favicon-16x16.png :</strong>
                    <img src="favicon-16x16.png" class="favicon-preview" id="png16-test" alt="PNG16">
                    <span id="png16-status">Test...</span>
                </div>
                <div>
                    <strong>apple-touch-icon.png :</strong>
                    <img src="apple-touch-icon.png" class="favicon-preview" id="apple-test" alt="Apple">
                    <span id="apple-status">Test...</span>
                </div>
            </div>
            
            <button onclick="testFavicons()">🔄 Retester</button>
            <button onclick="clearBrowserCache()">🗑️ Vider Cache</button>
        </div>
        
        <div class="solution">
            <h3>🚀 Solution 1 : Cache Busting (Recommandée)</h3>
            <p>Ajoutez un paramètre de version pour forcer le rechargement :</p>
            <div class="code">
&lt;link rel="icon" type="image/x-icon" href="favicon.ico?v=2025"&gt;<br>
&lt;link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png?v=2025"&gt;<br>
&lt;link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png?v=2025"&gt;
            </div>
            <button onclick="applyCacheBusting()">✅ Appliquer Cache Busting</button>
        </div>
        
        <div class="solution">
            <h3>🔄 Solution 2 : Vider le Cache Navigateur</h3>
            <p>Instructions pour vider le cache :</p>
            <ul>
                <li><strong>Chrome/Edge :</strong> Ctrl+Shift+Delete → Cocher "Images et fichiers en cache"</li>
                <li><strong>Firefox :</strong> Ctrl+Shift+Delete → Cocher "Cache"</li>
                <li><strong>Safari :</strong> Cmd+Option+E</li>
                <li><strong>Rechargement forcé :</strong> Ctrl+F5 (Windows) ou Cmd+Shift+R (Mac)</li>
            </ul>
        </div>
        
        <div class="solution">
            <h3>🔧 Solution 3 : Configuration Serveur</h3>
            <p>Ajoutez dans votre .htaccess :</p>
            <div class="code">
# Désactiver le cache pour les favicons<br>
&lt;FilesMatch "\.(ico|png)$"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;Header set Cache-Control "no-cache, no-store, must-revalidate"<br>
&nbsp;&nbsp;&nbsp;&nbsp;Header set Pragma "no-cache"<br>
&nbsp;&nbsp;&nbsp;&nbsp;Header set Expires 0<br>
&lt;/FilesMatch&gt;
            </div>
            <button onclick="showHtaccessCode()">📋 Copier Code .htaccess</button>
        </div>
        
        <div class="solution">
            <h3>📁 Solution 4 : Vérification des Permissions</h3>
            <p>Assurez-vous que les permissions sont correctes :</p>
            <div class="code">
chmod 644 favicon.ico<br>
chmod 644 favicon-*.png<br>
chmod 644 apple-touch-icon.png
            </div>
        </div>
        
        <div class="solution">
            <h3>🌐 Solution 5 : Test Direct des URLs</h3>
            <p>Testez l'accès direct aux fichiers :</p>
            <div>
                <a href="favicon.ico" target="_blank">🔗 favicon.ico</a> |
                <a href="favicon-32x32.png" target="_blank">🔗 favicon-32x32.png</a> |
                <a href="favicon-16x16.png" target="_blank">🔗 favicon-16x16.png</a> |
                <a href="apple-touch-icon.png" target="_blank">🔗 apple-touch-icon.png</a>
            </div>
            <p><small>Si ces liens ne fonctionnent pas, les fichiers ne sont pas accessibles sur le serveur.</small></p>
        </div>
        
        <div class="solution">
            <h3>⚡ Solution 6 : Rechargement Forcé</h3>
            <p>Méthodes pour forcer le rechargement :</p>
            <button onclick="forceReload()">🔄 Rechargement Forcé</button>
            <button onclick="openInIncognito()">🕵️ Mode Incognito</button>
            <div class="code">
// JavaScript pour forcer le rechargement<br>
window.location.reload(true);
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 Checklist de Diagnostic</h3>
            <div id="checklist">
                <label><input type="checkbox" id="check1"> Fichiers favicon uploadés sur le serveur</label><br>
                <label><input type="checkbox" id="check2"> Permissions 644 configurées</label><br>
                <label><input type="checkbox" id="check3"> URLs directes accessibles</label><br>
                <label><input type="checkbox" id="check4"> Cache navigateur vidé</label><br>
                <label><input type="checkbox" id="check5"> Cache busting appliqué (?v=2025)</label><br>
                <label><input type="checkbox" id="check6"> Test en mode incognito</label><br>
                <label><input type="checkbox" id="check7"> Configuration .htaccess mise à jour</label><br>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" style="background: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                🏠 Retour à l'Application
            </a>
        </div>
    </div>
    
    <script>
        function testFavicons() {
            const tests = [
                { id: 'ico-test', status: 'ico-status', src: 'favicon.ico' },
                { id: 'png32-test', status: 'png32-status', src: 'favicon-32x32.png' },
                { id: 'png16-test', status: 'png16-status', src: 'favicon-16x16.png' },
                { id: 'apple-test', status: 'apple-status', src: 'apple-touch-icon.png' }
            ];
            
            tests.forEach(test => {
                const img = document.getElementById(test.id);
                const status = document.getElementById(test.status);
                
                status.textContent = 'Test...';
                status.className = '';
                
                const newImg = new Image();
                newImg.onload = function() {
                    status.textContent = '✅ OK';
                    status.className = 'test-result success';
                };
                newImg.onerror = function() {
                    status.textContent = '❌ Erreur';
                    status.className = 'test-result error';
                };
                newImg.src = test.src + '?t=' + Date.now();
                
                img.src = newImg.src;
            });
        }
        
        function applyCacheBusting() {
            alert('✅ Cache busting appliqué sur cette page!\n\nPour l\'appliquer à votre application :\n1. Modifiez index.html\n2. Ajoutez ?v=2025 à tous les liens favicon\n3. Rechargez la page');
        }
        
        function clearBrowserCache() {
            alert('🗑️ Pour vider le cache :\n\n• Chrome/Edge : Ctrl+Shift+Delete\n• Firefox : Ctrl+Shift+Delete\n• Safari : Cmd+Option+E\n• Rechargement forcé : Ctrl+F5');
        }
        
        function showHtaccessCode() {
            const code = `# Désactiver le cache pour les favicons
<FilesMatch "\\.(ico|png)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires 0
</FilesMatch>`;
            
            navigator.clipboard.writeText(code).then(() => {
                alert('📋 Code .htaccess copié dans le presse-papiers !');
            }).catch(() => {
                prompt('📋 Copiez ce code dans votre .htaccess :', code);
            });
        }
        
        function forceReload() {
            window.location.reload(true);
        }
        
        function openInIncognito() {
            alert('🕵️ Ouvrez une fenêtre de navigation privée/incognito et testez votre site.\n\nCela permet de voir le site sans cache.');
        }
        
        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testFavicons, 1000);
        });
    </script>
</body>
</html>
