/* Application principale - Thème médical */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #e8f4fd 0%, #c8e6c9 100%);
  padding: 20px;
}

/* Composant PhoneInput */
.phone-input-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.phone-input-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(25, 118, 210, 0.15);
  max-width: 500px;
  width: 100%;
  text-align: center;
  border: 1px solid #e3f2fd;
}

.phone-input-card h2 {
  color: #1565c0;
  margin-bottom: 10px;
  font-size: 24px;
  font-weight: 600;
}

.phone-input-card p {
  color: #37474f;
  margin-bottom: 30px;
  font-size: 16px;
}

.phone-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-group {
  text-align: left;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1565c0;
}

.input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #b0bec5;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
  background-color: #fafafa;
}

.input-group input:focus {
  outline: none;
  border-color: #1976d2;
  background-color: white;
}

.input-group input.error {
  border-color: #d32f2f;
  background-color: #ffebee;
}

.error-message {
  color: #d32f2f;
  font-size: 14px;
  margin-top: 5px;
  display: block;
  font-weight: 500;
}

.verify-button {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

.verify-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(25, 118, 210, 0.4);
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
}

.verify-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #90a4ae;
}

/* Composant VaccinationList */
.vaccination-list-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(25, 118, 210, 0.15);
  border: 1px solid #e3f2fd;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.list-header h2 {
  color: #1565c0;
  margin: 0;
  font-weight: 600;
}

.list-header p {
  color: #37474f;
  margin: 0;
  font-weight: 500;
}

.back-button {
  background: #546e7a;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  box-shadow: 0 2px 4px rgba(84, 110, 122, 0.3);
}

.back-button:hover {
  background: #455a64;
  box-shadow: 0 4px 8px rgba(84, 110, 122, 0.4);
}

/* Tableau des vaccinations */
.vaccination-table {
  overflow-x: auto;
}

.vaccination-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.vaccination-table th,
.vaccination-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.vaccination-table td:last-child {
  text-align: center;
}

.vaccination-table tbody tr {
  transition: background-color 0.3s ease;
}

.vaccination-table tbody tr:hover {
  background-color: #f8f9fa;
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.vaccination-table th {
  background: linear-gradient(135deg, #e3f2fd 0%, #e8f5e8 100%);
  font-weight: 600;
  color: #1565c0;
  border-bottom: 2px solid #1976d2;
}

.patient-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.patient-name {
  font-weight: 600;
  color: #1565c0;
}

.patient-phone {
  font-size: 14px;
  color: #546e7a;
}

.vaccine-badge {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.view-button {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(25, 118, 210, 0.3);
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 100px;
  justify-content: center;
}

.view-button:hover {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(25, 118, 210, 0.4);
}

.view-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
}

.button-icon {
  font-size: 16px;
  display: inline-block;
  animation: pulse 2s infinite;
}

.button-text {
  font-size: 13px;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Composant VaccinationCard */
.vaccination-card-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.vaccination-card {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 2px solid #e3f2fd;
  background: linear-gradient(135deg, #e3f2fd 0%, #e8f5e8 100%);
  border-radius: 12px 12px 0 0;
}

.card-header h3 {
  margin: 0;
  color: #1565c0;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #546e7a;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-button:hover {
  background: #cfd8dc;
  color: #37474f;
}

.card-content {
  padding: 30px;
}

.patient-photo-section,
.patient-info,
.vaccination-info {
  margin-bottom: 30px;
}

.patient-photo-section h4,
.patient-info h4,
.vaccination-info h4 {
  color: #1565c0;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #1976d2;
  font-weight: 600;
}

.patient-photo-section {
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e3f2fd;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 10px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1976d2;
}

.info-item label {
  font-weight: 600;
  color: #546e7a;
  font-size: 14px;
}

.info-item span {
  color: #1565c0;
  font-size: 16px;
  font-weight: 500;
}

.vaccine-name {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  display: inline-block;
  width: fit-content;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

/* Messages d'erreur et états vides */
.error-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.error-container .error-message {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 15px 20px;
  border-radius: 8px;
  border: 1px solid #ef5350;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(198, 40, 40, 0.2);
  font-weight: 500;
}

.no-records {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.no-records h3 {
  color: #1565c0;
  margin-bottom: 15px;
  font-weight: 600;
}

.no-records p {
  color: #546e7a;
  margin-bottom: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .phone-input-card {
    padding: 30px 20px;
  }

  .vaccination-list-container {
    padding: 20px 15px;
  }

  .list-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .vaccination-table {
    font-size: 14px;
  }

  .vaccination-table th,
  .vaccination-table td {
    padding: 10px 8px;
  }

  .vaccination-card {
    margin: 10px;
    max-height: 95vh;
  }

  .card-content {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
