# 🏛️ Carnet de Vaccination Électronique - Production

## 📋 Description
Application web officielle pour la consultation du carnet de vaccination électronique de la République de Côte d'Ivoire, spécialisée dans la vérification de la vaccination contre la fièvre jaune.

## ✨ Fonctionnalités Principales

### 🔍 **Double Mode de Recherche**
- **📱 Par téléphone** : Recherche avec numéro international
- **👤 Par identité** : Recherche par nom, prénom et date de naissance

### 🎨 **Interface Moderne**
- Design responsive mobile/desktop
- Sélecteur de choix avec badge "OU"
- Thème vert officiel (#4caf50)
- Animations fluides et transitions élégantes

### 🏛️ **Identité Officielle**
- Logos du Ministère de la Santé et INHP
- Favicons gouvernementaux officiels
- Orthographe française correcte
- Couleurs institutionnelles

### 📱 **Support PWA**
- Installation possible comme application mobile
- Mode standalone pour expérience native
- Icônes optimisées pour tous les appareils

## 🚀 Déploiement Rapide

### **1. Upload des Fichiers**
Uploadez tout le contenu de ce dossier sur votre serveur web.

### **2. Permissions**
```bash
chmod 644 *.html *.jpg *.png *.ico *.svg *.txt *.xml *.md *.webmanifest
chmod 644 .htaccess _redirects
chmod 755 .
```

### **3. Test**
Accédez à `votre-domaine.com/verification-finale.html` pour valider le déploiement.

## 📁 Structure des Fichiers

### **🎯 Fichiers Essentiels**
- `index.html` - Application principale
- `logo_sante.jpg` - Logo Ministère de la Santé
- `logo_inhp.jpg` - Logo INHP
- `favicon.ico` - Favicon principal
- `site.webmanifest` - Configuration PWA

### **🧪 Fichiers de Test**
- `verification-finale.html` - Vérification complète du build
- `test-favicon.html` - Test des favicons officiels
- `diagnostic.html` - Diagnostic technique complet
- `test-simple.html` - Test basique du serveur

### **📚 Documentation**
- `BUILD-FINAL.md` - Résumé complet du build
- `DEPLOIEMENT-FINAL.md` - Guide de déploiement détaillé
- `README-ERREUR-500.md` - Solutions pour erreurs serveur

## 🔗 APIs Intégrées

### **API Téléphone**
- **URL** : `https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode`
- **Méthode** : POST
- **Paramètres** : `{tel: "numero", d: "PROD"}`

### **API Identité**
- **URL** : `https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcodeplus`
- **Méthode** : POST
- **Paramètres** : `{nompat: "NOM", prenompat: "PRENOM", datenaissancepat: "YYYY-MM-DD", d: "PROD"}`

## 🛠️ Technologies Utilisées

- **HTML5** - Structure moderne
- **CSS3** - Design responsive et animations
- **JavaScript ES6+** - Logique applicative
- **Fetch API** - Appels REST
- **PWA** - Progressive Web App

## 📱 Compatibilité

- **Navigateurs** : Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile** : iOS 12+, Android 8+
- **PWA** : Installation supportée sur tous les appareils modernes

## 🔧 Dépannage Rapide

### **Erreur 500**
1. Vérifiez les permissions des fichiers
2. Testez `test-simple.html`
3. Consultez `README-ERREUR-500.md`

### **Favicons manquants**
1. Vérifiez que tous les fichiers .ico et .png sont uploadés
2. Testez `test-favicon.html`
3. Videz le cache du navigateur

### **Accent manquant sur "fièvre"**
1. Videz le cache navigateur (Ctrl+F5)
2. Testez `test-accent.html`
3. Vérifiez l'encodage UTF-8 du serveur

## 📊 Performance

- **Taille** : ~3 MB (optimisé)
- **Chargement** : < 2 secondes
- **PWA Score** : 100/100
- **Mobile-friendly** : Oui

## 🎯 URLs Importantes

- **Application** : `/index.html`
- **Vérification** : `/verification-finale.html`
- **Test favicons** : `/test-favicon.html`
- **Diagnostic** : `/diagnostic.html`
- **Version secours** : `/index-backup.html`

## 📞 Support

Pour toute question technique, consultez :
1. `BUILD-FINAL.md` - Documentation complète
2. `diagnostic.html` - Outils de diagnostic
3. `verification-finale.html` - Tests automatiques

## 📄 Licence

Application officielle du Gouvernement de la République de Côte d'Ivoire.

---

**Version** : 2.1.0 (Final)  
**Date** : 2025-01-28  
**Statut** : ✅ Prêt pour production
