import React from 'react';
import type { VaccinationRecord } from '../services/api';

interface VaccinationCardProps {
  record: VaccinationRecord;
  onClose: () => void;
}

const VaccinationCard: React.FC<VaccinationCardProps> = ({ record, onClose }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateAge = (birthDate: string) => {
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  return (
    <div className="vaccination-card-overlay">
      <div className="vaccination-card">
        <div className="card-header">
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <span style={{ fontSize: '24px', color: '#1976d2' }}>🩺</span>
            <h3>Détails de la Vaccination</h3>
          </div>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="card-content">
          {/* Section Photo du Patient */}
          <div className="patient-photo-section">
            <h4>Photo du Patient</h4>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              {record.PHOTOPAT ? (
                <img
                  src={`https://opisms.net/ecarnet/upload/photo/${record.PHOTOPAT}`}
                  alt="Photo du patient"
                  style={{
                    width: '120px',
                    height: '120px',
                    borderRadius: '50%',
                    objectFit: 'cover',
                    border: '4px solid #1976d2',
                    boxShadow: '0 6px 12px rgba(25, 118, 210, 0.3)'
                  }}
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      parent.innerHTML = '<div style="color: #546e7a; font-style: italic;">📷 Photo non disponible</div>';
                    }
                  }}
                />
              ) : (
                <div style={{
                  color: '#546e7a',
                  fontStyle: 'italic',
                  padding: '40px',
                  border: '2px dashed #b0bec5',
                  borderRadius: '8px'
                }}>
                  📷 Photo non disponible
                </div>
              )}
            </div>
          </div>

          <div className="patient-info">
            <h4>Informations du Patient</h4>
            <div className="info-grid">
              <div className="info-item">
                <label>Nom:</label>
                <span>{record.NOMPAT}</span>
              </div>
              <div className="info-item">
                <label>Prénoms:</label>
                <span>{record.PRENOMPAT}</span>
              </div>
              <div className="info-item">
                <label>Sexe:</label>
                <span>{record.SEXEPAT === 'M' ? 'Masculin' : 'Féminin'}</span>
              </div>
              <div className="info-item">
                <label>Date de naissance:</label>
                <span>{formatDate(record.DATEPAT)} ({calculateAge(record.DATEPAT)} ans)</span>
              </div>
              <div className="info-item">
                <label>Numéro de téléphone:</label>
                <span>+{record.NUMEROPAT}</span>
              </div>
            </div>
          </div>

          <div className="vaccination-info">
            <h4>Informations de Vaccination</h4>
            <div className="info-grid">
              <div className="info-item">
                <label>Type de vaccin:</label>
                <span className="vaccine-name">{record.NOMVAC}</span>
              </div>
              <div className="info-item">
                <label>Date de vaccination:</label>
                <span>{formatDate(record.PRESENCE)}</span>
              </div>
              <div className="info-item">
                <label>Centre de vaccination:</label>
                <span>{record.NOMCENTR}</span>
              </div>
              <div className="info-item">
                <label>Lot de vaccin:</label>
                <span>{record.LOVAC}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VaccinationCard;
