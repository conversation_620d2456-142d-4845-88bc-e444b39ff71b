# Build final pour la production - Carnet de Vaccination Electronique
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "BUILD FINAL - CARNET DE VACCINATION ELECTRONIQUE" -ForegroundColor Green
Write-Host "Version: Drapeau Ivoirien v2.0 Final" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Nettoyer les anciens builds
Write-Host "Nettoyage des anciens builds..." -ForegroundColor Yellow
if (Test-Path "production-final") {
    Remove-Item -Recurse -Force "production-final"
}
if (Test-Path "carnet-vaccination-final.zip") {
    Remove-Item -Force "carnet-vaccination-final.zip"
}

# Créer le dossier de production final
New-Item -ItemType Directory -Name "production-final" | Out-Null
Write-Host "Dossier production-final cree" -ForegroundColor Green

# Copier les fichiers essentiels
Write-Host "Copie des fichiers de production..." -ForegroundColor Yellow
Copy-Item "dist\index.html" "production-final\"
Copy-Item "dist\vite.svg" "production-final\"
Copy-Item "src\assets\logo_inhp.jpg" "production-final\"
Copy-Item "src\assets\logo_sante.jpg" "production-final\"

# Créer .htaccess optimisé
Write-Host "Creation de .htaccess..." -ForegroundColor Yellow
@'
# Configuration Apache pour Carnet de Vaccination
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . index.html [L]
</IfModule>

# Cache optimisé
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>

# Sécurité
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
</IfModule>
'@ | Out-File -FilePath "production-final\.htaccess" -Encoding UTF8

# Créer _redirects
Write-Host "Creation de _redirects..." -ForegroundColor Yellow
@'
# Redirections pour plateformes cloud
/*    /index.html   200
/check/*    /check/index.html   200
'@ | Out-File -FilePath "production-final\_redirects" -Encoding UTF8

# Créer page de test finale
Write-Host "Creation de test.html..." -ForegroundColor Yellow
@'
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Carnet de Vaccination Final</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #fff3e0 0%, #e8f5e8 100%);
            min-height: 100vh; margin: 0; display: flex; align-items: center; justify-content: center;
        }
        .container {
            max-width: 600px; background: white; padding: 40px; border-radius: 15px;
            box-shadow: 0 8px 25px rgba(27, 94, 32, 0.15); text-align: center; border: 1px solid #c8e6c9;
        }
        .header { display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 20px; }
        .logo { width: 60px; height: 60px; border-radius: 8px; object-fit: contain; }
        .success { color: #1b5e20; font-weight: bold; font-size: 24px; margin-bottom: 15px; }
        .info { color: #e65100; margin-bottom: 15px; }
        .link { 
            display: inline-block; background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white; text-decoration: none; padding: 15px 30px; border-radius: 10px;
            font-weight: 600; margin-top: 20px; transition: transform 0.2s;
        }
        .link:hover { transform: translateY(-2px); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="logo_sante.jpg" alt="Ministère Santé" class="logo">
            <img src="logo_inhp.jpg" alt="INHP" class="logo">
        </div>
        <div class="success">Test de deploiement final reussi !</div>
        <p class="info">Application Carnet de Vaccination Electronique</p>
        <p class="info">Version: Drapeau Ivoirien v2.0 Final</p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
        <p><strong>Assets:</strong> Logos charges avec succes</p>
        <a href="index.html" class="link">Acceder a l'application</a>
    </div>
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('fr-FR');
    </script>
</body>
</html>
'@ | Out-File -FilePath "production-final\test.html" -Encoding UTF8

Write-Host "Fichiers de configuration crees" -ForegroundColor Green

# Créer la documentation finale
Write-Host "Creation de la documentation finale..." -ForegroundColor Yellow
@"
# Carnet de Vaccination Electronique - Version Finale

## Application Complete et Optimisee

Cette version finale presente toutes les fonctionnalites demandees :

### Design Harmonise Drapeau Ivoirien
- Page de saisie : En-tete orange avec logos (Sante 40px + INHP 100px) + bouton orange
- Page liste : Couleurs orange/blanc/vert + boutons harmonises
- Page carte : En-tete orange avec logos (Sante 40px + INHP 90px) + badge vert

### Logos Integres
- logo_sante.jpg : Ministere de la Sante (40px discret)
- logo_inhp.jpg : Institut National Hygiene Publique (90-100px dominant)
- Hierarchie claire : INHP prioritaire sur toutes les pages

### Navigation Optimisee
- Intelligence d'affichage : 1 resultat = carte directe, plusieurs = tableau
- Bouton retour corrige : Retourne a la liste (pas au formulaire)
- Transitions fluides entre les pages

### Interface Epuree
- IDs supprimes : En-tete et pied de page nettoyes
- Age cache : Plus affiche dans les informations patient
- Texte vaccin ameliore : "Vaccin fievre jaune effectuee"
- Espacement optimise : Logos et texte parfaitement alignes

## Contenu du Package Final

### Fichiers principaux :
- **index.html** : Application complete harmonisee
- **logo_sante.jpg** : Logo Ministere Sante
- **logo_inhp.jpg** : Logo INHP
- **vite.svg** : Favicon

### Configuration serveur :
- **.htaccess** : Apache avec cache, compression, securite
- **_redirects** : Support plateformes cloud
- **test.html** : Page de test avec les deux logos
- **README.md** : Cette documentation

## Deploiement sur https://opisms.net/check

### Instructions :
1. Copiez tout le contenu vers /check/ sur opisms.net
2. Verifiez les permissions (lecture pour tous)
3. Testez : https://opisms.net/check/test.html
4. Lancez : https://opisms.net/check/
5. Testez avec : 2250707983065

### Configuration technique :
- API : https://opisms.net/opisms-ws/api/v1/user/ecarnetqrcode
- Photos : https://opisms.net/ecarnet/upload/photo/
- Methode : POST avec {"tel": "numero", "d": "PROD"}

## Fonctionnalites Finales

### Page de saisie :
- En-tete orange avec logos Sante (40px) + INHP (100px)
- Bouton "Verifiez" orange degrade
- Validation temps reel du numero

### Page liste (plusieurs resultats) :
- Tableau harmonise orange/blanc/vert
- Boutons "Voir details" orange
- Bouton "Nouvelle recherche" orange

### Page carte (resultat unique ou details) :
- En-tete orange avec logos Sante (40px) + INHP (90px)
- Badge vaccin vert "Vaccin fievre jaune effectuee"
- Informations patient (age cache)
- Navigation contextuelle

## Compatibilite et Performance

- Navigateurs : Chrome, Firefox, Safari, Edge (IE11+)
- Appareils : Desktop, tablette, mobile
- Taille : ~25KB avec logos
- Performance : Chargement instantane
- Securite : Headers de securite configures

---
Build final genere le : $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Version : Drapeau Ivoirien v2.0 Final
Package : carnet-vaccination-final.zip
"@ | Out-File -FilePath "production-final\README.md" -Encoding UTF8

# Créer le package ZIP final
Write-Host "Creation du package ZIP final..." -ForegroundColor Yellow
try {
    Compress-Archive -Path "production-final\*" -DestinationPath "carnet-vaccination-final.zip" -Force
    Write-Host "Package ZIP final cree avec succes" -ForegroundColor Green
} catch {
    Write-Host "Erreur lors de la creation du ZIP" -ForegroundColor Red
    exit 1
}

# Statistiques finales
$files = Get-ChildItem "production-final" -File
$totalSize = ($files | Measure-Object -Property Length -Sum).Sum
$totalSizeKB = [math]::Round($totalSize / 1KB, 2)
$zipSize = (Get-Item "carnet-vaccination-final.zip").Length
$zipSizeKB = [math]::Round($zipSize / 1KB, 2)

# Affichage final
Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "BUILD FINAL TERMINE AVEC SUCCES !" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "PACKAGE FINAL CREE :" -ForegroundColor Cyan
Write-Host "  Archive : carnet-vaccination-final.zip ($zipSizeKB KB)" -ForegroundColor White
Write-Host "  Dossier : production-final/ ($totalSizeKB KB)" -ForegroundColor White
Write-Host "  Fichiers : $($files.Count)" -ForegroundColor White
Write-Host ""
Write-Host "CONTENU :" -ForegroundColor Cyan
$files | Sort-Object Name | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    $sizeStr = if ($size -lt 1) { "$([math]::Round($_.Length)) B" } else { "$size KB" }
    Write-Host "  $($_.Name) ($sizeStr)" -ForegroundColor Green
}
Write-Host ""
Write-Host "DEPLOIEMENT :" -ForegroundColor Yellow
Write-Host "  1. Copiez le contenu vers /check/ sur opisms.net" -ForegroundColor White
Write-Host "  2. Testez : https://opisms.net/check/test.html" -ForegroundColor White
Write-Host "  3. Lancez : https://opisms.net/check/" -ForegroundColor White
Write-Host ""
Write-Host "APPLICATION FINALE PRETE POUR LA PRODUCTION !" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan
